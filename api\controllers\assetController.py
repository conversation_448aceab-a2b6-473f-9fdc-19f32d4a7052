from sqlalchemy import String, and_, func, or_

from api.models.uamModels import *
from api.models.assetModels import *
from api.models.hseModels import *

from api import db
from sqlalchemy.sql import cast
from sqlalchemy.types import String, JSON
from api.utils.utils import roles
from sqlalchemy.orm import joinedload,contains_eager
from sqlalchemy.dialects.postgresql import ARRAY 

import requests

def add_new_camera(data):
    
    verified_modules = Modules.get_ids_by_list(list(set(data["modules"])))
    if len(verified_modules) == 0 :
        return {"success": False, "message": "Modules does not exist"}
    else:
        verified_modules.sort()
        new_camera= Cameras.create(data,verified_modules)
        return {
            "success": True,
            "message": "Camera added successfully",
            "data":{
                "client_id": new_camera.client_id,
                "factory_id": new_camera.factory_id,
                "area_id": new_camera.area_id,
                "sub_area_id": new_camera.sub_area_id,
                "camera_id": new_camera.camera_id if new_camera and new_camera.camera_id else "",
                "camera_ip": new_camera.camera_ip if new_camera and new_camera.camera_ip else "",
                "camera_name": new_camera.camera_name if new_camera and new_camera.camera_name else "",
                "camera_position_no": new_camera.camera_position_no if new_camera and new_camera.camera_position_no else "",
                "nvr_no": new_camera.nvr_no if new_camera and new_camera.nvr_no else "",
                "username": new_camera.username if new_camera and new_camera.username else "",
                "password": new_camera.password if new_camera and new_camera.password else "",
                "stream": new_camera.stream if new_camera and new_camera.stream else "",
                "port": new_camera.port if new_camera and new_camera.port else "",
                "modules": Modules.get_records_by_list(verified_modules),  
                "active": new_camera.active
            }
        }
        

def get_cameras(data):
  
    client_id = data.get("client_id")
    factory_id = data.get("factory_id")
    modules = data.get("modules", [])
    
    # Get active cameras for the specified client and factory
    query = Cameras.query.filter_by(
        client_id=client_id,
        factory_id=factory_id,
        active=True
    )
    
    # If modules are specified, filter cameras that have any of these modules
    if modules:
        query = query.filter(Cameras.modules.overlap(cast(modules, ARRAY(db.Integer))))
    
    cameras = query.all()
    
    # Format the camera data
    result = []
    for camera in cameras:
        result.append({
            "camera_id": camera.camera_id,
            "camera_name": camera.camera_name,
            "camera_ip": camera.camera_ip,
            "camera_position_no": camera.camera_position_no,
            "nvr_no": camera.nvr_no,
            "username": camera.username,
            "password": camera.password,
            "stream": camera.stream,
            "port": camera.port,
            "modules": camera.modules,
            "module_name": Modules.get_id_name_by_list(camera.modules),
            "area_id": camera.area_id,
            "sub_area_id": camera.sub_area_id,
            "active": camera.active
        })
    
    return result  # Always return the list, even if empty
