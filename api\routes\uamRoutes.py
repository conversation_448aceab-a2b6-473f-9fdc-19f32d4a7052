from datetime import datetime
import json
import os
from flask import request
from flask_restx import Resource, abort

from api.models.uamModels import *
from api.models.assetModels import *

from api import uam_ns
from api.payloads.uamPayloads import *
from api.controllers import uamController as uam_func

from werkzeug.security import check_password_hash, generate_password_hash
from werkzeug.utils import secure_filename
from configparser import ConfigParser
from api import app,db
from api.services import token_required, user_token_required
from api.utils.utils import generate_jwt_token

from sqlalchemy.orm import joinedload
import traceback

config_parser = ConfigParser()
config_parser.read("config.cfg")

@uam_ns.route("/add_log")
@uam_ns.doc("Add a Log")
class AddActivityLog(Resource):
    @uam_ns.doc("Add a Log")
    @uam_ns.response(201, "Log added successfully.")   
    def put(self):
        data = uam_ns.payload

        try:
            resp = uam_func.add_log(data)
            return {"message": resp["message"], 
                    "success": resp["success"],
            }

        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"}) 


@uam_ns.route("/get_all_Users")
@uam_ns.doc("Get all users")
class GetAllUsers(Resource):
    # @token_required
    @uam_ns.doc("Get all users")
    @uam_ns.response(200, "Users fetched successfully.")
    # @uam_ns.doc(security=["Authorization", "Token-Type"])
    def get(self):
        #print("Started at: ", datetime.datetime.now())
        results=uam_func.get_all_user()
        #print("Ended at: ", datetime.datetime.now()) 
        return {"message": "Users fetched successfully.", "data": results}, 200



@uam_ns.route("/login_with_email")
@uam_ns.doc("Login user with email")
class LoginUserWithEmail(Resource):
    @uam_ns.doc("Login user")
    @uam_ns.expect(login_user_paylaod)
    @uam_ns.response(200, "User logged in successfully.")
    @uam_ns.response(400, "Validation Error")
    def post(self):
        data = uam_ns.payload
        try:
            req_fields = ["email", "password"]
            for field in req_fields:
                if not data.get(field):
                    return {"message": f"{field} is missing."}, 400

            get_user = Users.get_by_email(data.get("email"))
            if not get_user:
                return {"message": "User does not exist"}, 400
            
            

            if check_password_hash(get_user.password, data["password"]):
                
                    data['user_id'] = get_user.user_id
                    data['factory_id'] = get_user.factory_id
                    data['client_id'] = get_user.client_id
                    data['role_name'] = get_user.roles.role.role_name
                    data['role_id'] = get_user.roles.role_id
                    
                    jwt_token = generate_jwt_token(data)
                    new_log = UserLog.create({
                        'user_id': get_user.user_id,
                        'login_time': datetime.datetime.utcnow(),
                    })
                    return {
                        "message": "User logged In successfully.",
                        "data":{
                        "id": get_user.user_id,
                        "email": get_user.email,
                        "name": get_user.name if get_user.name else "",
                        "role_name": get_user.roles.role.role_name,
                        "role_id": get_user.roles.role_id,
                        "accessToken": jwt_token,
                        "area_ids": {"id":get_user.areas[0].area_id, "name":get_user.areas[0].area.name} if get_user.areas and get_user.areas[0].area else None,
                        "factory": {"id":get_user.factories[0].factory_id, "name":get_user.factories[0].factory.name} if get_user.factories else None,
                        "client": {
                            "id": get_user.client_id,
                            "name": Client.get_name_by_id(get_user.client_id) if get_user.client_id else None
                        } if get_user.client_id else None,
                        }
                    }, 200
                
            else:
                return {"message": "Invalid email and password combination"}, 400

        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})

@uam_ns.route('/logout_time/<int:userID>')
@uam_ns.doc("Set the logout time for a user")
class AddLogoutTime(Resource):
    @uam_ns.doc("Set the logout time for a user")
    @uam_ns.response(200, "User logged out time successfully added.")
    @uam_ns.response(400, "Validation Error")
    def put(self, userID):
        try:
            UserLog.set_logout_time(userID)
            UserLog.set_duration_time(userID)
            UserLog.set_accumulated_duration(userID)
            return {"message": "Logout time succesfully saved"}, 200
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})

@uam_ns.route('/get_user_logs')
@uam_ns.doc("Get all user logs")
class GetUserLogs(Resource):
    @uam_ns.doc("Get all user logs")
    @uam_ns.response(200, "User logs fetched successfully.")
    @uam_ns.response(400, "Validation Error")
    def get(self):
        try:

            resp = uam_func.user_log_data()

            if resp:
                return {"message": "User logs fetched successfully.", "data": resp}, 200
            else:
                return {"message": "No data found."}, 400

        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})



@uam_ns.route("/get_user/<int:id>")
@uam_ns.doc("Get user Details")
class GetUserDetails(Resource):
    @token_required
    @uam_ns.doc("Get user Details")
    @uam_ns.response(200, "User fetched successfully.")
    @uam_ns.doc(security=["Authorization", "Token-Type"])
    def get(self, id):
        try:
            if not id:
                return {"message": f"User ID is required."}, 400

            get_user = Users.get_by_user_id(id)
            if not get_user:
                return {"message": "User does not exist"}, 400
            else:
                resp= uam_func.get_user_data(get_user.user_id)
                return {"message": "User fetched successfully.", "data": resp}, 200
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})


@uam_ns.route("/add_user")
@uam_ns.doc("Add new User")
class AddUser(Resource):
    #@token_required
    @uam_ns.expect(add_user_paylaod)
    @uam_ns.response(200, "User added successfully.")
    @uam_ns.response(400, "Validation Error")
    #@uam_ns.doc(security=["Authorization", "Token-Type"])
    def post(self):
        data = uam_ns.payload
        try:
            # Validate required fields
            req_fields = ["name", "email", "role_id", "mobile_no", "password"]
            for field in req_fields:
                if field not in data or not data.get(field):
                    return {"message": f"{field} is required"}, 400 

            resp= uam_func.add_user(data)
            
            return {"message": resp["message"], "data": resp["data"] if "data" in resp else None, "status": resp["status"]}, 200 if resp["status"] else 400

        except Exception as e:
            print(e)
            return {"message": f"An error occurred: {str(e)}"}, 400
      

@uam_ns.route("/update_user")
@uam_ns.doc("Update new User") 
class UpdateUser(Resource):
    @token_required
    @uam_ns.doc("Update new User")
    @uam_ns.expect(update_user_paylaod)
    @uam_ns.response(200, "User updated successfully.")
    @uam_ns.response(400, "Validation Error")
    @uam_ns.doc(security=["Authorization", "Token-Type"])
    def put(self):
        data = uam_ns.payload
        try:
            req_fields = ["email", "user_id", "role_id", "name","mobile_no","factories","admin_id"]
            for field in req_fields:
                if not data.get(field):
                    return {"message": f"{field} is missing."}, 400

            get_user = Users.get_by_user_id(data.get("user_id"))
            if not get_user:
                return {"message": "User does not exist"}, 400

            existing_user = Users.get_by_email_other(data.get("email"), data["user_id"])
            if existing_user:
                return {"message": "User with this email exists."}, 400

            get_role = Role.get_by_id(data["role_id"])
            if not get_role:
                return {"message": "User Role does not exists."}, 400

            response=uam_func.update_user(get_user,data,get_role)
            return response, 200 if response["status"] else 400

        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})


@uam_ns.route("/update_user_status/<int:id>")
@uam_ns.doc("Update User Status")
class UpdateUserStatus(Resource):
    # @token_required
    @uam_ns.doc("Update user status")
    @uam_ns.response(200, "Status updated successfully.")
    @uam_ns.response(400, "Validation Error")
    # @uam_ns.doc(security=["Authorization", "Token-Type"])
    def put(self, id):
        try:
            if not id:
                return {"message": f"User ID is required."}, 400

            resp = Users.toggle_user_active(id)
            if resp is not None:
                return {
                    "message": "Status updated successfully",
                    "success": True,
                    "data": {
                        "id": resp.user_id,
                        "active": resp.active,
                    },
                }, 200
            else:
                return {
                    "message": "User does not exist",
                    "success": False,
                }, 400

        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})


@uam_ns.route("/delete_user/<int:id>")
@uam_ns.doc("Delete User")
class DeleteUser(Resource):
    @token_required
    @uam_ns.doc("Delete User")
    @uam_ns.response(200, "User deleted successfully.")
    @uam_ns.response(400, "Validation Error")
    @uam_ns.doc(security=["Authorization", "Token-Type"])
    def delete(self, id):
        try:
            if not id:
                return {"message": f"User ID is missing."}, 400

            resp = Users.delete_user(id)
            return {
                "message": (
                    "User deleted successfully" if resp else "User does not exist"
                ),
                "success": True if resp else False,
            }
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})

@uam_ns.route('/get_activity_log')
@uam_ns.doc("Activity Log")
class GetActivityLog(Resource):
    @uam_ns.expect(activity_log_payload)
    @uam_ns.response(200, "Activity Log Fetched Successfully.")
    @uam_ns.response(400, "Validation Error")
    def put(self):
        try:
            payloadData = uam_ns.payload
            data = uam_func.get_activity_log(payloadData)
            return {
                "message": "Activity Logs obtained successfully",
                "success": True,
                "data": data
            }
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})
            
            
            
@uam_ns.route("/get_all_roles")
@uam_ns.doc("Get all Roles")
class GetAllRoles(Resource):
    @user_token_required
    @uam_ns.doc("Get all Roles")
    @uam_ns.response(200, "Roles fetched successfully.")
    @uam_ns.doc(security=["Authorization", "Token-Type"])
    def get(self):
        get_roles = Role.get_all()
        results = [
        {
            "role_id": role.role_id,
            "role_name": role.role_name,
            "permissions": [
                { "role_id": perm.permission.permission_id,"role_name": perm.permission.permission_name}
                for perm in role.role_permissions
            ]
            } for role in get_roles
        ]
        
        return {"message": "Roles fetched successfully.", "data": results}, 200


@uam_ns.route("/add_role")
@uam_ns.doc("Add new Role")
class AddRole(Resource):
    @token_required
    @uam_ns.doc("Add new Role")
    @uam_ns.expect(add_role_paylaod)
    @uam_ns.response(200, "Role added successfully.")
    @uam_ns.response(400, "Validation Error")
    @uam_ns.doc(security=["Authorization", "Token-Type"])
    def post(self):
        data = uam_ns.payload
        try:
            req_fields = ["role_name", "description"]
            for field in req_fields:
                if not data.get(field):
                    return {"message": f"{field} is missing."}, 400

            if not data["role_name"].replace(" ", "").isalpha(): 
                return {"message": "Role name should only contain alphabets."}, 400
            
            get_role = Role.get_by_name(data["role_name"].strip())
            if get_role:
                return {"message": "User Role already exists."}, 400

            resp= Role.create(data["role_name"].strip(), data["description"])
            
            return {"message": "Role added successfully", "data":{
                        "role_id": resp.role_id,
                        "role_name": resp.role_name,
                        }}
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})


@uam_ns.route("/update_role")
@uam_ns.doc("Update Role")
class UpdateRole(Resource):
    @token_required
    @uam_ns.doc("Update Role")
    @uam_ns.expect(update_role_paylaod)
    @uam_ns.response(200, "Role updated successfully.")
    @uam_ns.response(400, "Validation Error")
    @uam_ns.doc(security=["Authorization", "Token-Type"])
    def put(self):
        data = uam_ns.payload
        try:
            req_fields = ["role_id","role_name", "description"]
            for field in req_fields:
                if not data.get(field):
                    return {"message": f"{field} is missing."}, 400
            
            if not data["role_name"].replace(" ", "").isalpha():
                return {"message": "Role name should only contain alphabets."}, 400

            get_role = Role.get_by_id(data["role_id"])
            if not get_role:
                return {"message": "User Role does not exist"}, 400
            
            resp= Role.update(get_role,data["role_name"].strip(), data["description"])
            return {"message": "Role updated successfully", 
                    "data":{
                        "role_id": resp.role_id,
                        "role_name": resp.role_name,
                        }
                    }
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})


@uam_ns.route("/delete_role/<int:id>")
@uam_ns.doc("delete Role")
class DeleteRole(Resource):
    @token_required
    @uam_ns.doc("Delete Role")
    @uam_ns.response(200, "Role deleted successfully.")
    @uam_ns.response(400, "Validation Error")
    @uam_ns.doc(security=["Authorization", "Token-Type"])
    def delete(self, id):
        try:
            if not id:
                    return {"message": f"Role ID is missing."}, 400
            
            get_role = Role.get_by_id(id)
            if not get_role:
                return {"message": "User Role does not exist"}, 400

            Role.delete(get_role)
            
            return {"message": "Role deleted successfully" }
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})


# ROLE PERMISSIONS

@uam_ns.route("/add_role_permission")
@uam_ns.doc("Add Role Permission")
class AddRolePermission(Resource):
    @token_required
    @uam_ns.doc("Add Role Permission")
    @uam_ns.expect(add_role_permission_paylaod)
    @uam_ns.response(200, "Role Permission added successfully.")
    @uam_ns.response(400, "Validation Error")
    @uam_ns.doc(security=["Authorization", "Token-Type"])
    def post(self):
        data = uam_ns.payload
        try:
            req_fields = ["role_id", "permission_id"]
            for field in req_fields:
                if not data.get(field):
                    return {"message": f"{field} is missing."}, 400


            get_role = Role.get_by_id(data["role_id"])
            if not get_role:
                return {"message": "User Role does not exist"}, 400
            
            get_permission= Permission.get_by_id(data["permission_id"])
            if not get_permission:
                return {"message": "Permissions does not exist"}, 400
            
            existing_role_permission=RolePermission.get_by_role_and_permission(get_role.role_id, get_permission.permission_id)
            if existing_role_permission:
                 return {"message": "Role Permission already exists."}, 400
             
            resp= RolePermission.create(get_role.role_id, get_permission.permission_id)
            
            return {"message": "Role Permission successfully", "data":{
                        "role_id": resp.role.role_id,
                        "role_name": resp.role.role_name,
                        "permission_id": resp.permission.permission_id,
                        "permission_name": resp.permission.permission_name,
                        }}
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})


@uam_ns.route("/delete_role_permission")
@uam_ns.doc("Delete Role Permission")
class DeletePermission(Resource):
    @token_required
    @uam_ns.doc("Delete Role Permission")
    @uam_ns.expect(delete_role_permission_paylaod)
    @uam_ns.response(200, "Role Permission deleted successfully.")
    @uam_ns.response(400, "Validation Error")
    @uam_ns.doc(security=["Authorization", "Token-Type"])
    def delete(self):
        data = uam_ns.payload
        try:
            req_fields = ["role_id", "permission_id"]
            for field in req_fields:
                if not data.get(field):
                    return {"message": f"{field} is missing."}, 400

            get_role = Role.get_by_id(data["role_id"])
            if not get_role:
                return {"message": "User Role does not exist"}, 400
            
            get_permission= Permission.get_by_id(data["permission_id"])
            if not get_permission:
                return {"message": "Permissions does not exist"}, 400
            
            existing_role_permission=RolePermission.get_by_role_and_permission(get_role.role_id, get_permission.permission_id)
            if not existing_role_permission:
                 return {"message": "Role Permission does not exist"}, 400
             
            RolePermission.delete(existing_role_permission)
            
            return {"message": "Role Permission deleted", 
                    "success": True
                  }
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})


@uam_ns.route("/get_role_permissions/<int:id>")
@uam_ns.doc("Get Role Permissions")
class GetRolePermissions(Resource):
    @user_token_required
    @uam_ns.doc("Get Role Permissions")
    @uam_ns.response(200, "Role Permissions fetched successfully.")
    @uam_ns.doc(security=["Authorization", "Token-Type"])
    def get(self,id):
        try:
            if not id:
                return {"message": "Role ID is required"}, 400
            
            get_role = Role.get_by_id(id)
            if not get_role:
                 return {"message": "Role does not exist"}, 400
             
            results = [
            {
                "role_id": perm.permission.permission_id,
                "role_name": perm.permission.permission_name,
                
                } for perm in get_role.role_permissions
            ]
            
            return {"message": "Role Permissions fetched successfully.", "data": {
                  "role_id": get_role.role_id,
                  "role_name": get_role.role_name,
                  "permissions": results,
                
                }}, 200
        
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})