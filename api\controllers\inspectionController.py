from api import db

from api.utils.utils import *
from api.models.uamModels import *
from api.models.inspectionModel import *

from sqlalchemy import and_, or_, func, case
#from sqlalchemy.dialects.postgresql import ARRAY

from collections import defaultdict
from datetime import datetime, timedelta
import calendar

# ----------------------------------------- All Reels Data --------------------------------


def get_all_reels_count(data):
    try:
        start_date, end_date = parse_filters(data)
        
        result = (
            db.session.query(
                
                func.sum(
                    case(
                        (InspectionAlertObjects.object == 'match', InspectionAlertCounts.count),
                        else_=0
                    )
                ).label('match_reels'),
                func.sum(
                    case(
                        (InspectionAlertObjects.object == 'mismatch', InspectionAlertCounts.count),
                        else_=0
                    )
                ).label('mismatch_reels'),
                func.sum(
                    case(
                        (InspectionAlertObjects.object == 'wrong mismatch', InspectionAlertCounts.count),
                        else_=0
                    )
                ).label('wrong_mismatch_reels')
            )
            .select_from(InspectionAlertCounts)
            .filter(
                InspectionAlertCounts.date.between(start_date, end_date)
            )
            .join(
                InspectionModels,
                and_(
                    InspectionAlertCounts.model_id == InspectionModels.model_id,
                    InspectionModels.model == 'reel'
                )
            )
            .join(
                InspectionAlertObjects,
                InspectionAlertCounts.object_id == InspectionAlertObjects.object_id
            )
            .one()
        )

        match_reels = result.match_reels or 0
        mismatch_reels = result.mismatch_reels or 0
        wrong_mismatch_reels = result.wrong_mismatch_reels or 0
        total_reels = match_reels + mismatch_reels
        
        return {
            "total_reels": total_reels,
            "match_reels": match_reels,
            "mismatch_reels": mismatch_reels,
            "wrong_mismatch_reels": wrong_mismatch_reels
        }

    except Exception as e:
        print(f"Error in get_all_reels_data: {e}")


# ----------------------------------------- Wrong mismatch Reasons  --------------------------------
def get_wrong_mismatch_reasons_count(data):
    try:
        start_date, end_date = parse_filters(data)
        print("start_date: ", start_date, "end_date: ", end_date)

        result = (
            db.session.query(
                InspectionValidationReason.validation_reason,
                func.count(InspectionValidationReason.validation_reason_id).label(
                    'count')
            )
            .select_from(InspectionAlertValidation)  # Start from Alerts table
            .filter(
                InspectionAlertValidation.validation_timestamp.between(
                    start_date, end_date)
            )
            .join(
                InspectionAlertObjects,
                InspectionAlertValidation.object_id == InspectionAlertObjects.object_id
            )
            .filter(InspectionAlertObjects.object == 'wrong mismatch')
            .join(
                InspectionValidationReason,
                InspectionAlertValidation.validation_reason_id == InspectionValidationReason.validation_reason_id
            )
            .group_by(InspectionValidationReason.validation_reason)
            .all()
        )
        print("Result: ", result)
        # Initialize counts dictionary with zeros
        counts = {
            "camera_problem": 0,
            "time_problem": 0,
            "detection_problem": 0
        }

        for reason, count in result:
            if reason == 'Camera Problem':
                counts['camera_problem'] = count
            elif reason == 'Time Problem':
                counts['time_problem'] = count
            elif reason == 'Detection Problem':
                counts['detection_problem'] = count
        print("Counts: ", counts)
        return counts

    except Exception as e:
        print(f"Error in get_wrong_mismatch_reasons_count: {e}")


# ----------------------------------------- Reprocess Data --------------------------------
def get_reprocess_data_count(data):
    try:
        start_date, end_date = parse_filters(data)
        filters = data.get('filters', {})

        # Determine grouping format based on which filter has data
        if filters.get('week'):
            group_by = 'day'
            format_str = '%A'  # Day name (Monday, Tuesday, etc.)
            init_counts = {day: 0 for day in ['Monday', 'Tuesday', 'Wednesday',
                                              'Thursday', 'Friday', 'Saturday', 'Sunday']}
        elif filters.get('month'):
            group_by = 'day'
            format_str = '%d'  # Just the day number
            # Initialize with all days of the month
            month = filters.get('month')
            year, month_num = month.split('-')
            year = int(year)
            month_num = int(month_num)
            # Get the number of days in the month
            _, last_day = calendar.monthrange(year, month_num)
            # Initialize counts with all days of the month
            init_counts = {str(day): 0 for day in range(1, last_day + 1)}
        else:  # date (hourly)
            group_by = 'hour'
            # HH:00 (will be converted to AM/PM format later)
            format_str = '%H:00'
            # Initialize with AM/PM format
            init_counts = {}
            for hour in range(24):
                if hour == 0:
                    init_counts["12:00 AM"] = 0
                elif hour < 12:
                    init_counts[f"{hour}:00 AM"] = 0
                elif hour == 12:
                    init_counts["12:00 PM"] = 0
                else:
                    init_counts[f"{hour-12}:00 PM"] = 0

        # Single query for all cases
        result = (
            db.session.query(
                func.date_trunc(group_by, InspectionOperations.timestamp).label(
                    'grouped_timestamp'),
                func.count(InspectionOperations.operation_id).label('count')
            )
            .join(
                InspectionOperationTypes,
                InspectionOperations.operation_type_id == InspectionOperationTypes.operation_type_id
            )
            .filter(
                InspectionOperationTypes.operation_type == 'sticker reprocess',
                InspectionOperations.timestamp.between(start_date, end_date)
            )
            .group_by('grouped_timestamp')
            .order_by('grouped_timestamp')
            .all()
        )

        # For month view, use pre-initialized counts
        if filters.get('month'):
            counts = init_counts
            for timestamp, count in result:
                key = timestamp.strftime(format_str)
                counts[key] = count
        else:
            # For week and date views, use pre-initialized counts
            counts = init_counts
            for timestamp, count in result:
                hour = timestamp.hour
                # Convert to AM/PM format for hourly view
                if group_by == 'hour':
                    if hour == 0:
                        key = "12:00 AM"
                    elif hour < 12:
                        key = f"{hour}:00 AM"
                    elif hour == 12:
                        key = "12:00 PM"
                    else:
                        key = f"{hour-12}:00 PM"
                else:
                    key = timestamp.strftime(format_str)
                counts[key] = count

        return counts

    except Exception as e:
        print(f"Error in get_reprocess_data_count: {e}")
        return None


# ----------------------------------------- Get All User Operation Details  --------------------------------
def get_user_operation_details(data):
    try:
        start_date, end_date = parse_filters(data)
        print("start_date: ", start_date, "end_date: ", end_date)

    
        records = (
            db.session.query(
                InspectionOperations.user_id,
                InspectionOperationTypes.operation_type,
                func.count(InspectionOperations.operation_type_id).label(
                    'count')
            )
            .select_from(InspectionOperations)
            .filter(
                InspectionOperations.timestamp.between(
                    start_date, end_date)
            )
            .join(
                InspectionOperationTypes,
                InspectionOperations.operation_type_id == InspectionOperationTypes.operation_type_id
            )
            .join(
                Users,
                InspectionOperations.user_id == Users.user_id
            )
            .group_by(InspectionOperations.user_id, InspectionOperationTypes.operation_type)
            .all()
        )

        #print(records)

        # Collect all unique operation types dynamically
        operation_types = set(snake_case(operation_type) for _, operation_type, _ in records)

        # Group and accumulate user data
        user_data = defaultdict(lambda: {'user_id': 0})  # Start with just user_id

        for user_id, operation_type, count in records:
            user_data[user_id]['user_id'] = user_id
            user_data[user_id][snake_case(operation_type)] = count

        # Get mismatch count 
        wrong_mismatch_result = (
            db.session.query(
                InspectionAlertValidation.user_id,
                InspectionValidationReason.validation_reason,
                func.count(InspectionValidationReason.validation_reason_id).label(
                    'count')
            )
            .select_from(InspectionAlertValidation)  # Start from Alerts table
            .filter(
                InspectionAlertValidation.validation_timestamp.between(
                    start_date, end_date)
            )
            .join(
                InspectionAlertObjects,
                InspectionAlertValidation.object_id == InspectionAlertObjects.object_id
            )
            .filter(InspectionAlertObjects.object == 'wrong mismatch')
            .join(
                InspectionValidationReason,
                InspectionAlertValidation.validation_reason_id == InspectionValidationReason.validation_reason_id
            )
            .join(
                Users,
                InspectionAlertValidation.user_id == Users.user_id
            )
            .group_by(InspectionAlertValidation.user_id, InspectionValidationReason.validation_reason)
            .all()
        )
        
        for user_id, validation_reason, count in wrong_mismatch_result:
            user_data[user_id]['user_id'] = user_id
            user_data[user_id]['total_wrong_mismatch'] = int(user_data[user_id].get('total_wrong_mismatch') or 0) + count
            user_data[user_id][snake_case(validation_reason)] = count
            

        validation_reason_types = set(snake_case(validation_reason) for _, validation_reason, _ in wrong_mismatch_result)
        
        # Ensure all operation types exist per user, defaulting to 0
        for user_dict in user_data.values():
            
            for operation_type in operation_types:
                user_dict.setdefault(snake_case(operation_type), 0)
            
            if user_dict.get('total_wrong_mismatch') is None:
                user_dict['total_wrong_mismatch'] = 0
            for validation_reason in validation_reason_types:
                user_dict.setdefault(snake_case(validation_reason), 0)
            
            user = Users.get_user(user_dict['user_id'])
            if user is not None:
                user_dict['user_name'] = user.name
                user_dict['user_email'] = user.email

        # Convert to list
        data = list(user_data.values())

        # Final output
        # print("Data: ", data)
        return data

    except Exception as e:
        print(f"Error in get_user_operation_details: {e}")