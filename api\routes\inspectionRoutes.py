from flask_restx import Resource, abort
from api.models.assetModels import *
from api.models.uamModels import *

from api import inspection_ns
from api import db
#from api.services import token_required, user_token_required
#from sqlalchemy import and_, or_

from api.models.inspectionModel import *
from api.controllers import inspectionController 
from api import inspection_ns
from api.payloads.inspectionPayloads import *
import re

@inspection_ns.route("/sync_rows")
@inspection_ns.doc("Sync Rows")
class SyncRows(Resource):
    @inspection_ns.doc("Sync Rows")
    @inspection_ns.expect(inspection_sync_payload_model)
    @inspection_ns.response(200, "Rows Synced successfully.")
    @inspection_ns.response(400, "Validation Error")
    def post(self):
        data = inspection_ns.payload
        try:
            models=[InspectionAlertValidation, InspectionOperations, InspectionAlertCounts] 
            for field in models:
                if field.__tablename__ in data.keys():
                    rows = data[field.__tablename__]
                    any_rows_added = field.add_multiple_rows(rows) 
                    
             # Generate the appropriate message
            if any_rows_added:
                message = "Rows Synced successfully."
            else:
                message = "No rows were added."
            
            response = {"message": message}  
                
            return response, 200 

        except Exception as e:
            db.session.rollback()  # Rollback any failed transactions
            print(str(e))
            abort(400, {
                "success": False,
                "message": "Failed to sync rows",
                "error": str(e)
            })

# ---------------------------- FOR All Reels Data ----------------------------

@inspection_ns.route("/get_all_reels_count")
@inspection_ns.doc("Get All Reels Data Count")
class GetAllReelsData(Resource):
    # @user_token_required
    @inspection_ns.expect(filters_payload)
    @inspection_ns.response(200, "Reels data count fetched successfully.")
    @inspection_ns.response(400, "Validation Error")
    # @factory_dashboard_ns.doc(security=["Authorization", "Token-Type"])
    def put(self):
        """
        Returns All reels counts:
           -  "total_reels"
           -  "match_reels"
           -  "mismatch_reels"
           -  "wrong_mismatch_reels"
         
        """
        data = inspection_ns.payload
        try:
            req_fields = ["factory_id"] # leaving array here incase multiple fields are required in the future 
            for field in req_fields:
                if field not in data or not data[field.strip()]:
                    return {"message": f"{field} is missing."}, 400
        
            #----------- Validate formats with a loop -------------------------------
            patterns = {
                "week": r"\d{4}-W\d{2}",
                "month": r"\d{4}-\d{2}",
                "date": r"\d{4}-\d{2}-\d{2}",
                "starting": r"\d{4}-\d{2}-\d{2}",
                "ending": r"\d{4}-\d{2}-\d{2}"
            }

            f = data.get("filters", {})
            for field, pattern in patterns.items():
                if f.get(field) and not re.match(pattern, f[field]):
                    return {"message": f"Invalid {field} format"}, 400
            

            
            resp = inspectionController.get_all_reels_count(data)
            return {"message": "Reels data count fetched successfully.",
                    "success": True,
                    "data": resp
            },200
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})


# ---------------------------- FOR Wrong Mismatch Reasons ----------------------------

@inspection_ns.route("/get_wrong_mismatch_reasons_count")
@inspection_ns.doc("Get All Wrong Mistmatch Reasons Data Count")
class GetWrongMismatchReasonsData(Resource):
    # @user_token_required
    @inspection_ns.expect(filters_payload)
    @inspection_ns.response(200, "Wrong Mismatch Reasons data Count fetched successfully.")
    @inspection_ns.response(400, "Validation Error")
    # @factory_dashboard_ns.doc(security=["Authorization", "Token-Type"])
    def put(self):
        """
         
        """
        data = inspection_ns.payload
        try:
            # leaving array here incase multiple fields are required in the future
            req_fields = ["factory_id"]
            for field in req_fields:
                if field not in data or not data[field.strip()]:
                    return {"message": f"{field} is missing."}, 400

            # ----------- Validate formats with a loop -------------------------------
            patterns = {
                "week": r"\d{4}-W\d{2}",
                "month": r"\d{4}-\d{2}",
                "date": r"\d{4}-\d{2}-\d{2}",
                "starting": r"\d{4}-\d{2}-\d{2}",
                "ending": r"\d{4}-\d{2}-\d{2}"
            }

            f = data.get("filters", {})
            for field, pattern in patterns.items():
                if f.get(field) and not re.match(pattern, f[field]):
                    return {"message": f"Invalid {field} format"}, 400


            resp = inspectionController.get_wrong_mismatch_reasons_count(data)
            return {"message": "Wrong Mismatch Reasons data fetched successfully.",
                    "success": True,
                    "data": resp
                    }, 200
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})


# ---------------------------- Reprocesses Data  ----------------------------

@inspection_ns.route("/get_reprocess_data_count")
@inspection_ns.doc("Get Sticker Reprocesses Data")
class GetReprocessData(Resource):
    # @user_token_required
    @inspection_ns.expect(filters_payload)
    @inspection_ns.response(200, "Reprocess data count fetched successfully.")
    @inspection_ns.response(400, "Validation Error")
    # @factory_dashboard_ns.doc(security=["Authorization", "Token-Type"])
    def put(self):
        """
         
        """
        data = inspection_ns.payload
        try:
            # leaving array here incase multiple fields are required in the future
            req_fields = ["factory_id"]
            for field in req_fields:
                if field not in data or not data[field.strip()]:
                    return {"message": f"{field} is missing."}, 400

            # ----------- Validate formats with a loop -------------------------------
            patterns = {
                "week": r"\d{4}-W\d{2}",
                "month": r"\d{4}-\d{2}",
                "date": r"\d{4}-\d{2}-\d{2}",
                "starting": r"\d{4}-\d{2}-\d{2}",
                "ending": r"\d{4}-\d{2}-\d{2}"
            }

            f = data.get("filters", {})
            for field, pattern in patterns.items():
                if f.get(field) and not re.match(pattern, f[field]):
                    return {"message": f"Invalid {field} format"}, 400



            resp = inspectionController.get_reprocess_data_count(data)
            return {"message": "Reprocess data count fetched successfully.",
                    "success": True,
                    "data": resp
                    }, 200
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})


@inspection_ns.route("/get_user_operation_details")
@inspection_ns.doc("Get All User Operation Details")
class GetUserOperationDetails(Resource):
    # @user_token_required
    @inspection_ns.expect(filters_payload)
    @inspection_ns.response(200, "User Operation Details fetched successfully.")
    @inspection_ns.response(400, "Validation Error")
    # @factory_dashboard_ns.doc(security=["Authorization", "Token-Type"])
    def put(self):
        """
         
        """
        data = inspection_ns.payload
        try:
            # leaving array here incase multiple fields are required in the future
            req_fields = ["factory_id", "client_id"]
            for field in req_fields:
                if field not in data or not data[field.strip()]:
                    return {"message": f"{field} is missing."}, 400
            # ----------- Validate formats with a loop -------------------------------
            patterns = {
                "week": r"\d{4}-W\d{2}",
                "month": r"\d{4}-\d{2}",
                "date": r"\d{4}-\d{2}-\d{2}",
                "starting": r"\d{4}-\d{2}-\d{2}",
                "ending": r"\d{4}-\d{2}-\d{2}"
            }

            f = data.get("filters", {})
            for field, pattern in patterns.items():
                if f.get(field) and not re.match(pattern, f[field]):
                    return {"message": f"Invalid {field} format"}, 400
            

            resp = inspectionController.get_user_operation_details(data)
            return {"message": "User Operation Details fetched successfully.",
                    "success": True,
                    "data": resp
                    }, 200
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})