from flask_restx import Resource, abort

from api.models.uamModels import *
from api.models.assetModels import *
from api.models.hseModels import *


from api import hse_ns
from api.payloads.hsePayloads import *
from api.services import token_required



@hse_ns.route("/get_all_modules")
@hse_ns.doc("Get all Modules")
class GetAllModules(Resource):
    # @token_required
    @hse_ns.doc("Get all Modules")
    @hse_ns.response(200, "Modules fetched successfully.")
    # @hse_ns.doc(security=["Authorization", "Token-Type"])
    def get(self):
        get_modules = Modules.get_all_records()
        results = [
        {
            "module_id": module.module_id,
            "module_name": module.name,
        } for module in get_modules
        ]
        return {"message": "Modules fetched successfully.", "data": results}, 200


@hse_ns.route("/add_update_module")
@hse_ns.doc("Add or Update Module")
class AddOrUpdateModule(Resource):
    # @token_required
    @hse_ns.doc("Add or Update Module")
    @hse_ns.expect(add_module_payload)
    @hse_ns.response(200, "Module added or updated successfully.")
    @hse_ns.response(400, "Validation Error")
    # @hse_ns.doc(security=["Authorization", "Token-Type"])
    def post(self):
        data = hse_ns.payload
        try:
            req_fields = ["name"]
            for field in req_fields:
                if field not in data or not data[field.strip()]:
                    return {"message": f"{field} is missing."}, 400
            module, action = Modules.createUpdate(data)
            if action == "updated":
                msg = f"{module.name} module updated successfully"
            else:
                msg = f"{module.name} module added successfully"
            return {
                "message": msg,
                "success": True,
            }, 200
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})


@hse_ns.route("/sync_alerts")
@hse_ns.doc("Sync Alerts")
class SyncAlerts(Resource):
    # @token_required
    @hse_ns.doc("Sync Alerts")
    @hse_ns.expect(insert_alerts_payload)
    @hse_ns.response(200, "Alerts synced successfully.")
    @hse_ns.response(400, "Validation Error")
    # @hse_ns.doc(security=["Authorization", "Token-Type"])
    def post(self):
        data = hse_ns.payload
        try:
            # Only check for 'alerts' at the root
            if "alerts" not in data or not data["alerts"]:
                return {"message": "alerts is missing."}, 400

            # Required fields inside each alert
            alert_fields = [
                "alert_id", "factory_id", "client_id", "module_id",
                "compliant", "unannotated_url", "annotated_url", "camera_id"
            ]
            for idx, alert in enumerate(data["alerts"]):
                for field in alert_fields:
                    if field not in alert or alert[field] in [None, ""]:
                        return {"message": f"{field} is missing in alert at index {idx}."}, 400

            resp = Alerts.sync_alerts(data["alerts"])

            return {
                "message": "Alerts synced successfully",
                "success": True,
            }, 200

        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})

# @hse_ns.route("delete_module/<int:id>")
# @hse_ns.doc("Delete Module")
# class DeleteModule(Resource):
#     # @token_required
#     @hse_ns.doc("Delete Module")
#     @hse_ns.response(200, "Module deleted successfully.")
#     @hse_ns.response(400, "Validation Error")
#     # @hse_ns.doc(security=["Authorization", "Token-Type"])
#     def delete(self, id):
#         try:
#             if not id:
#                 return {"message": "Module id is missing."}, 400
            
#             module = Modules.get_by_id(id)
#             if not module:
#                 return {"message": "Module does not exist"}, 400
            
#             Modules.delete(module)
#             return {"message": "Module deleted successfully" , "success": True }, 200
            
#         except Exception as e:
#             print(e)
#             abort(400, {"success": False, "message": f"Error {e}"})