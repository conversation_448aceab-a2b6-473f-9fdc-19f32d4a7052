from api import db
import datetime
#from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import ARRAY
from sqlalchemy import desc, func, asc

class InspectionAlertObjects(db.Model):
    __tablename__ = 'inspection_alert_objects'
    object_id = db.Column(db.Integer, primary_key=True)
    client_id = db.Column(db.Integer,  db.ForeignKey('ast_client.client_id'))
    factory_id = db.Column(db.Integer,  db.ForeignKey('ast_factory.factory_id'))
    object = db.Column(db.Text, nullable=False)
    timestamp = db.Column(db.DateTime, default=datetime.datetime.now(datetime.timezone.utc))

    @classmethod
    def get_all_records(cls):
        return cls.query.all()
    
    @classmethod
    def create(cls, data):
        new_rec = cls(
            client_id=data["client_id"],
            factory_id=data["factory_id"],
            object=data["object"],
            timestamp=datetime.datetime.now(datetime.timezone.utc)
        )
        db.session.add(new_rec)
        db.session.commit()
        return new_rec
    
class InspectionValidationReason(db.Model):
    __tablename__ = 'inspection_validation_reason'
    validation_reason_id = db.Column(db.Integer, primary_key=True)
    client_id = db.Column(db.Integer,  db.ForeignKey('ast_client.client_id'))
    factory_id = db.Column(db.Integer,  db.ForeignKey('ast_factory.factory_id'))
    validation_reason = db.Column(db.Text, nullable=False)
    timestamp = db.Column(db.DateTime, default=datetime.datetime.now(datetime.timezone.utc))
    
    @classmethod
    def get_all_records(cls):
        return cls.query.all()
    
    @classmethod
    def create(cls, data):
        new_rec = cls(
            client_id=data["client_id"],
            factory_id=data["factory_id"],
            validation_reason=data["validation_reason"],
            timestamp=datetime.datetime.now(datetime.timezone.utc)
        )
        db.session.add(new_rec)
        db.session.commit()
        return new_rec
    
class InspectionModels(db.Model):
    __tablename__ = 'inspection_models'
    model_id = db.Column(db.Integer, primary_key=True)
    client_id = db.Column(db.Integer,  db.ForeignKey('ast_client.client_id'))
    factory_id = db.Column(db.Integer,  db.ForeignKey('ast_factory.factory_id'))
    model = db.Column(db.Text, nullable=False)
    timestamp = db.Column(db.DateTime, default=datetime.datetime.now(datetime.timezone.utc))
    
    @classmethod
    def get_all_records(cls):
        return cls.query.all()
    
    @classmethod
    def create(cls, data):
        new_rec = cls(
            client_id=data["client_id"],
            factory_id=data["factory_id"],
            model=data["model"],
            timestamp=datetime.datetime.now(datetime.timezone.utc)
        )
        db.session.add(new_rec)
        db.session.commit()
        return new_rec


class InspectionAlertValidation(db.Model):
    __tablename__ = 'inspection_alert_validation'
    id = db.Column(db.Integer, primary_key=True)
    client_id = db.Column(db.Integer,  db.ForeignKey('ast_client.client_id'))
    factory_id = db.Column(db.Integer,  db.ForeignKey('ast_factory.factory_id'))
    user_id = db.Column(db.Integer,  db.ForeignKey('uam_user.user_id'))
    object_id = db.Column(db.Integer,  db.ForeignKey('inspection_alert_objects.object_id'))
    model_id = db.Column(db.Integer,  db.ForeignKey('inspection_models.model_id'))
    validation_status = db.Column(db.Boolean, default=False, nullable=True)
    validation_reason_id = db.Column(db.Integer,  db.ForeignKey('inspection_validation_reason.validation_reason_id'), nullable=True)
    validation_timestamp = db.Column(db.DateTime, nullable=True)
    frame_id = db.Column(db.Integer)
    timestamp = db.Column(db.DateTime, default=datetime.datetime.now(datetime.timezone.utc))

    @classmethod
    def get_all_records(cls):
        return cls.query.all()

    @classmethod
    def add_multiple_rows(cls, data):
        rows = []
        for item in data:
            validation_reason_id = item.get("validation_reason_id", None)
            if validation_reason_id:
                new_record = cls(
                    client_id= item.get("client_id", None),
                    factory_id=item.get("factory_id", None),
                    user_id=item.get("user_id", None),
                    object_id=item.get("object_id", None),
                    model_id=item.get("model_id", None),
                    frame_id=item.get("frame_id", None),
                    validation_status = True,
                    validation_reason_id = item.get("validation_reason_id", None),
                    validation_timestamp = datetime.datetime.now(datetime.timezone.utc),
                    timestamp=datetime.datetime.now(datetime.timezone.utc)
                )
            else:
                new_record = cls(
                    client_id= item.get("client_id", None),
                    factory_id=item.get("factory_id", None),
                    user_id=item.get("user_id", None),
                    object_id=item.get("object_id", None),
                    model_id=item.get("model_id", None),
                    frame_id=item.get("frame_id", None),
                    timestamp=datetime.datetime.now(datetime.timezone.utc)
                )
            rows.append(new_record)

        db.session.add_all(rows)
        db.session.commit()
        return True
        
    @classmethod
    def batch_update_validation(cls, validations):
        success = True
        for data in validations:
            alert_id = data.get('alert_id')
            # Check if alert exists
            alert_exists = cls.query.filter_by(alert_id=alert_id).first()
            
            if not alert_exists:
                print(f"Alert ID {alert_id} does not exist")
                success = False
                continue
                
            # Update validation fields for existing alert
            alert_exists.validation_status = data.get('validation_status')
            alert_exists.validation_reason_id = data.get('validation_reason_id')
            alert_exists.validation_timestamp = datetime.datetime.now(datetime.timezone.utc)
                
        db.session.commit()
        return success
        

class InspectionAlertCounts(db.Model):
    __tablename__ = 'inspection_alert_counts'
    alert_id = db.Column(db.Integer, primary_key=True)
    count = db.Column(db.Integer, nullable=False)
    client_id = db.Column(db.Integer,  db.ForeignKey('ast_client.client_id'))
    factory_id = db.Column(db.Integer,  db.ForeignKey('ast_factory.factory_id'))
    object_id = db.Column(db.Integer,  db.ForeignKey('inspection_alert_objects.object_id'))
    model_id = db.Column(db.Integer,  db.ForeignKey('inspection_models.model_id'))
    batch = db.Column(db.Integer, default=0, nullable=False)
    date = db.Column(db.Date, nullable=False)

    @classmethod
    def add_multiple_rows(cls, dataset):
        rows = []

        for data in dataset:
            date=datetime.datetime.strptime(data['date'], "%Y-%m-%d")

            count=data['count']
            client_id=data['client_id']
            factory_id=data['factory_id']
            object_id=data['object_id']
            model_id=data['model_id']
         
            record_exists=cls.query.filter_by(date=date,client_id=client_id,factory_id=factory_id,object_id=object_id,model_id=model_id).order_by(desc(cls.batch)).first()
            
            if record_exists:
                if record_exists.count <= count:
                    #Update the count value
                    record_exists.count=count
                    db.session.commit()
                else:
                    #Create a new batch
                    new_record = cls(
                        date=date,
                        count=count,
                        client_id=client_id,
                        factory_id=factory_id,
                        object_id=object_id,
                        model_id=model_id,
                        batch=record_exists.batch+1
                    )
                    rows.append(new_record)
            else:
                new_record = cls(
                    date=date,
                    count=count,
                    client_id=client_id,
                    factory_id=factory_id,
                    object_id=object_id,
                    model_id=model_id,
                    batch=1
                )
                rows.append(new_record)

        if rows:
            db.session.add_all(rows)
            db.session.commit()
        return True


class InspectionAlertFrames(db.Model):
    __tablename__ = "inspection_alert_frames"
    frame_id = db.Column(db.Integer, primary_key=True)
    client_id = db.Column(db.Integer, db.ForeignKey("ast_client.client_id"))
    factory_id = db.Column(db.Integer, db.ForeignKey("ast_factory.factory_id"))
    image_url = db.Column(db.Text, nullable=False)
    timestamp = db.Column(db.DateTime, nullable=False, default=datetime.datetime.now(datetime.timezone.utc))
    
    @classmethod
    def get_all_records(cls):
        return cls.query.all()
    
    @classmethod
    def create(cls, data):
        new_rec = cls(
            client_id=data["client_id"],
            factory_id=data["factory_id"],
            image_url=data["image_url"],
            timestamp=datetime.datetime.now(datetime.timezone.utc)
        )
        db.session.add(new_rec)
        db.session.commit()
        return new_rec



class InspectionOperationTypes(db.Model):
    __tablename__ = 'inspection_operation_types'
    operation_type_id = db.Column(db.Integer, primary_key=True)
    client_id = db.Column(db.Integer,  db.ForeignKey('ast_client.client_id'))
    factory_id = db.Column(db.Integer,  db.ForeignKey('ast_factory.factory_id'))
    operation_type = db.Column(db.Text, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.datetime.now(datetime.timezone.utc))

    @classmethod
    def get_all_records(cls):
        return cls.query.all()
    
    @classmethod
    def create(cls, data):
        new_rec = cls(
            client_id=data["client_id"],
            factory_id=data["factory_id"],
            operation_type=data["operation_type"],
            created_at=datetime.datetime.now(datetime.timezone.utc)
        )
        db.session.add(new_rec)
        db.session.commit()
        return new_rec
        
class InspectionOperations(db.Model):
    __tablename__ = 'inspection_operations'
    operation_id = db.Column(db.Integer, primary_key=True)
    client_id = db.Column(db.Integer,  db.ForeignKey('ast_client.client_id'))
    factory_id = db.Column(db.Integer,  db.ForeignKey('ast_factory.factory_id'))
    user_id = db.Column(db.Integer,  db.ForeignKey('uam_user.user_id'))
    operation_type_id = db.Column(db.Integer,  db.ForeignKey('inspection_operation_types.operation_type_id'))
    timestamp = db.Column(db.DateTime, default=datetime.datetime.now(datetime.timezone.utc))
    
    @classmethod
    def get_all_records(cls):
        return cls.query.all()

    @classmethod
    def add_multiple_rows(cls, data):
        rows = []
        for item in data:
            new_record = cls(
                client_id= item.get("client_id", None),
                factory_id=item.get("factory_id", None),
                user_id=item.get("user_id", None),
                operation_type_id=item["operation_type_id"],
                timestamp=datetime.datetime.now(datetime.timezone.utc)
                #timestamp=datetime.strptime(item["datetime"], "%Y-%m-%dT%H:%M:%S.%fZ"),
            )
            rows.append(new_record)

        db.session.add_all(rows)
        db.session.commit()
        return True