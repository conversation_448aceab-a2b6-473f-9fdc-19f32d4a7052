from api import facial_ns
from flask_restx import fields



# FacialEmployees Swagger Model
facial_employee_model = facial_ns.model(
    "Facial Employee",
    {
        "id": fields.Integer(required=True, description="Local DB ID"),
        "client_id": fields.Integer(required=True, description="Client ID"),
        "factory_id": fields.Integer(required=True, description="Factory ID"),
        "employee_id": fields.String(required=True, description="Employee ID"),
        "name": fields.String(required=True, description="Employee name"),
        "designation": fields.String(description="Designation"),
        "department": fields.String(description="Department"),
        "blacklisted": fields.<PERSON><PERSON>an(description="Is Blacklisted", default=False),
    },
)

# FacialEmployeeEmbeddings Swagger Model
facial_employee_embedding_model = facial_ns.model(
    "Facial Employee Embedding",
    {
        "id": fields.Integer(required=True, description="Local DB ID"),
        "client_id": fields.Integer(required=True, description="Client ID"),
        "factory_id": fields.Integer(required=True, description="Factory ID"),
        "employee_id": fields.String(required=True, description="Employee ID"),
        "embedding_vector": fields.String(required=True, description="Embedding vector (base64 or JSON string)"),
    },
)

# FacialRecognitionLogs Swagger Model
facial_recognition_log_model = facial_ns.model(
    "Facial Recognition Log",
    {
        "client_id": fields.Integer(required=True, description="Client ID"),
        "factory_id": fields.Integer(required=True, description="Factory ID"),
        "employee_id": fields.String(description="Linked Employee ID"),
        "log_id": fields.Integer(required=True, description="Local DB Log ID"),
        "camera_id": fields.String(required=True, description="Camera ID"),
        "detection_datetime": fields.DateTime(description="Datetime of detection"),
        "person_detected": fields.String(required=True, description="Detected person's name"),
        "sop_followed": fields.Boolean(description="SOP followed", default=False),
        "face_image_url": fields.String(required=True, description="URL of the detected face image"),
    },
)

# Define the main payload model
facial_sync_payload_model = facial_ns.model(
    "Facial Sync Payload",
    {
        "facial_employees": fields.List(
            fields.Nested(facial_employee_model),
            required=True,
            description="List of employee records",
        ),
        "facial_employee_embeddings": fields.List(
            fields.Nested(facial_employee_embedding_model),
            required=True,
            description="List of employee embeddings records",
        ),
        "facial_recognition_logs": fields.List(
            fields.Nested(facial_recognition_log_model),
            required=True,
            description="List of facial recognition log records",
        ),
    },
)


facial_live_logs_payload = facial_ns.model(
    "Facial Live Logs Payload",
    {
        "client_id": fields.Integer(required=True, description="Client ID"),
        "factory_id": fields.Integer(required=True, description="Factory ID"),
        "limit": fields.Integer(required=False, description="Number of logs to return", default=20),
        "camera_id": fields.String(required=False, description="Filter by camera ID"),
        "employee_id": fields.String(required=False, description="Filter by employee ID"),
        "filters": fields.Nested(
            facial_ns.model(
                "LiveLogsFilters",
                {
                    "date": fields.String(required=False, description="Specific date (YYYY-MM-DD)"),
                    "week": fields.String(required=False, description="Week (YYYY-Www, e.g., 2024-W28)"),
                    "month": fields.String(required=False, description="Month (YYYY-MM)"),
                    "starting": fields.String(required=False, description="Start date (YYYY-MM-DD)"),
                    "ending": fields.String(required=False, description="End date (YYYY-MM-DD)"),
                }
            ),
            required=False,
            description="Date filters"
        ),
    },
)

facial_stats = facial_ns.model(
    "Facial Stats",
    {
        "client_id": fields.Integer(required=True, description="Client ID"),
        "factory_id": fields.Integer(required=True, description="Factory ID"),
        "camera_id": fields.String(required=False, description="Filter by camera ID"),
        "employee_id": fields.String(required=False, description="Filter by employee ID"),
        "filters": fields.Nested(
            facial_ns.model(
                "StatsFilters",
                {
                    "date": fields.String(required=False, description="Specific date (YYYY-MM-DD)"),
                    "week": fields.String(required=False, description="Week (YYYY-Www, e.g., 2024-W28)"),
                    "month": fields.String(required=False, description="Month (YYYY-MM)"),
                    "starting": fields.String(required=False, description="Start date (YYYY-MM-DD)"),
                    "ending": fields.String(required=False, description="End date (YYYY-MM-DD)"),
                }
            ),
            required=False,
            description="Date filters"
        ),
    },
)

cameraStatus = facial_ns.model(
    "Camera Status",
    {
        "client_id": fields.Integer(required=True, description="Client ID"),
        "factory_id": fields.Integer(required=True, description="Factory ID"),
        "camera_id": fields.String(required=False, description="Filter by camera ID"),
        "employee_id": fields.String(required=False, description="Filter by employee ID"),
        "filters": fields.Nested(
            facial_ns.model(
                "StatsFilters",
                {
                    "date": fields.String(required=False, description="Specific date (YYYY-MM-DD)"),
                    "week": fields.String(required=False, description="Week (YYYY-Www, e.g., 2024-W28)"),
                    "month": fields.String(required=False, description="Month (YYYY-MM)"),
                    "starting": fields.String(required=False, description="Start date (YYYY-MM-DD)"),
                    "ending": fields.String(required=False, description="End date (YYYY-MM-DD)"),
                }
            ),
            required=False,
            description="Date filters"
        ),
    },
)

daily_movement_summary_payload = facial_ns.model(
    "Facial Stats",
    {
        "client_id": fields.Integer(required=True, description="Client ID"),
        "factory_id": fields.Integer(required=True, description="Factory ID"),
        "camera_id": fields.String(required=False, description="Filter by camera ID"),
        "employee_id": fields.String(required=False, description="Filter by employee ID"),
        "filters": fields.Nested(
            facial_ns.model(
                "StatsFilters",
                {
                    "shift": fields.String(required=False, description="Shift"),
                    "date": fields.String(required=False, description="Specific date (YYYY-MM-DD)"),
                    "week": fields.String(required=False, description="Week (YYYY-Www, e.g., 2024-W28)"),
                    "month": fields.String(required=False, description="Month (YYYY-MM)"),
                    "starting": fields.String(required=False, description="Start date (YYYY-MM-DD)"),
                    "ending": fields.String(required=False, description="End date (YYYY-MM-DD)"),
                }
            ),
            required=False,
            description="Date filters"
        ),
    },
)