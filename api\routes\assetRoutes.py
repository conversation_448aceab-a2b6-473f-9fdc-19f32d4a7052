from flask_restx import Resource, abort
from sqlalchemy import or_

from api.models.uamModels import *
from api.models.assetModels import *

from api.payloads.assetPayloads import *
from api import asset_ns
from api.controllers import assetController as asset_func

from werkzeug.utils import secure_filename
from api.services import token_required, user_token_required
from api.utils.utils import ALLOWED_EXTENSIONS, allowed_file
from flask import request
import os
import logging
from botocore.exceptions import ClientError
from configparser import ConfigParser
from api import app, db
from datetime import datetime




@asset_ns.route("/get_all_clients")
@asset_ns.doc("Get all Clients")
class GetAllClients(Resource):
    #@user_token_required 
    @asset_ns.doc("Get all Clients")
    @asset_ns.response(200, "Clients fetched successfully.")
    #@asset_ns.doc(security=["Authorization", "Token-Type"])
    def get(self): 
        get_clients = Client.get_all_records()
        results = [
        {
            "client_id": client.client_id,
            "name": client.name,
            "logo": client.logo,
            "address": client.address,
            "created_at": client.created_at.strftime("%Y-%m-%d"),
            "updated_at": client.updated_at.strftime("%Y-%m-%d"),
            "active": client.active
        } for client in get_clients if client.active 
        ] 
        
        return {"message": "Clients fetched successfully.", "data": results}, 200


@asset_ns.route("/add_client")
@asset_ns.doc("Add new Client")
class AddClient(Resource):
    #@token_required
    @asset_ns.doc("Add new Client")
    @asset_ns.expect(add_client_payload)
    @asset_ns.response(200, "Client added successfully.")
    @asset_ns.response(400, "Validation Error")
    #@asset_ns.doc(security=["Authorization", "Token-Type"])
    def post(self):
        try:
            req_fields = ["name", "file", "address"]
            for field in req_fields:
                if not add_client_payload.parse_args()[field]:
                    return {"message": f"{field} is missing."}, 400
            
            data=add_client_payload.parse_args()
            
            data["logo"]= "logo"
            resp= Client.create(data)
        
            return {"message": "Client added successfully", "data":{
                        "client_id": resp.client_id,
                        "name": resp.name,
                        "logo": resp.logo,
                        "address": resp.address
                        }}
                        
            # if "file" not in request.files:
                # return {"message": "File is not selected"}, 400

            # file = request.files["file"]

            # if file.filename == "":
                # return {"message": "File is not selected"}, 400
            
            # if file and not allowed_file(file.filename):
                    # return {"message": f"Invalid picture. Allowed types are {ALLOWED_EXTENSIONS}"}, 400
            # else:
                # filename = secure_filename(file.filename)
                # client_name = data["name"]
                # FILE_NAME_IMAGE = f"Logo_{client_name}_{datetime.now().strftime('%Y_%m_%d_%H_%M_%S')}_{filename}"
                
                # file_path = os.path.join("api/static/client/", FILE_NAME_IMAGE)
                # file.save(file_path)
                # try:
                    # s3.upload_file("api/static/client/" + FILE_NAME_IMAGE, BUCKET_NAME , FILE_NAME_IMAGE,
                                # ExtraArgs={"Tagging": 'public=yes'}, )

                    # data["logo"]= FILE_NAME_IMAGE
                    # resp= Client.create(data)
                
                    # return {"message": "Client added successfully", "data":{
                                # "client_id": resp.client_id,
                                # "name": resp.name,
                                # "logo": resp.logo,
                                # "address": resp.address
                                # }}
                # except ClientError as e:
                    # print("Error")
                    # logging.error(e)
                    # return {"message": "Failed to update logo. Try again"}, 400
                
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})



@asset_ns.route("/delete_client/<int:id>")
@asset_ns.doc("Delete Client")
class DeleteClient(Resource):
    @token_required
    @asset_ns.doc("Delete Client")
    @asset_ns.response(200, "Client deleted successfully.")
    @asset_ns.response(400, "Validation Error")
    @asset_ns.doc(security=["Authorization", "Token-Type"])
    def delete(self, id):
        try:
            if not id:
                return {"message": f"Client ID is missing."}, 400
            
            get_client = Client.get_by_id(id)
            if not get_client:
                return {"message": "Client does not exist"}, 400
            
            Client.delete(get_client)
            return {"message": "Client deleted successfully" , "success": True }, 200
            
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})


@asset_ns.route("/update_client_status/<int:id>")
@asset_ns.doc("Update Client Status")
class UpdateClientStatus(Resource):
    @token_required
    @asset_ns.doc("Update client status")
    @asset_ns.response(200, "Status updated successfully.")
    @asset_ns.response(400, "Validation Error")
    @asset_ns.doc(security=["Authorization", "Token-Type"])
    def put(self,id):
        try:
            if not id:
                    return {"message": f"Client ID is required."}, 400
            
            resp= Client.toggle_status(id) 
            if resp is not None:
                return {
                    "message":  "Status updated successfully",
                    "success":  True,
                    "data":{
                            "id": resp.client_id,
                            "name": resp.name,
                            "logo": resp.logo,
                            "address": resp.address,
                            "created_at": resp.created_at.strftime("%Y-%m-%d"),
                            "updated_at": resp.updated_at.strftime("%Y-%m-%d"),
                            "active": resp.active
                            }
                    }, 200
            else:
                return {
                "message": "Client does not exist" ,
                "success":  False,
                
                }, 400

        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})


@asset_ns.route("/get_client/<int:id>")
@asset_ns.doc("Get Client by ID")
class GetClient(Resource):
    @user_token_required
    @asset_ns.doc("Get client by ID")
    @asset_ns.response(200, "Client fetched successfully.")
    @asset_ns.response(400, "Validation Error")
    @asset_ns.doc(security=["Authorization", "Token-Type"])
    def get(self, id):
        try:
            if not id:
                    return {"message": f"Client ID is required."}, 400
                
            resp = Client.get_by_id(id) 
            if resp is not None:
                return {
                    "message":  "Client fetched successfully",
                    "success":  True,
                    "data":{
                            "id": resp.client_id,
                            "name": resp.name,
                            "logo": resp.logo,
                            "address": resp.address,
                            "created_at": resp.created_at.strftime("%Y-%m-%d"),
                            "updated_at": resp.updated_at.strftime("%Y-%m-%d"),
                            "active": resp.active,
                            }
                    }, 200
            else:
                return {
                "message": "Client does not exist" ,
                "success":  False,
                
                }, 400

        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})
            
            
            
            
@asset_ns.route("/get_all_factories")
@asset_ns.doc("Get all Factories")
class GetAllFactories(Resource):
    #@user_token_required 
    @asset_ns.doc("Get all Factories")
    @asset_ns.response(200, "Factories fetched successfully.")
    #@asset_ns.doc(security=["Authorization", "Token-Type"])
    def get(self): 
        get_factories = Factory.get_all_records()
        results = [
        {
            "factory_id": factory.factory_id,
            "name": factory.name,
            "address": factory.address,
            "created_at": factory.created_at.strftime("%Y-%m-%d"),
            "updated_at": factory.updated_at.strftime("%Y-%m-%d"),
            "active": factory.active,
            "areas": [ { 
                                    "id": x.area_id, 
                                    "area": x.name,
                                    "areaOwner": x.users_areas[0].user.name if x.users_areas  and x.users_areas[0].user and x.users_areas[0].user.roles.role.role_name  == "Area" else "", 
                                    "active": x.active if x.active else False,
                                    "sub_area": [ {"id": sub.sub_area_id, "name": sub.name} for sub in x.sub_areas if sub.active]
                                         } for x in factory.factory_areas ] 
        } for factory in get_factories if factory.active 
        ] 
        
        return {"message": "Factories fetched successfully.", "data": results}, 200


@asset_ns.route("/add_factory")
@asset_ns.doc("Add new Factory")
class AddFactory(Resource):
    #@token_required
    @asset_ns.doc("Add new Factory")
    @asset_ns.expect(add_factory_payload)
    @asset_ns.response(200, "Factory added successfully.")
    @asset_ns.response(400, "Validation Error")
    #@asset_ns.doc(security=["Authorization", "Token-Type"])
    def post(self):
        try:
            req_fields = ["client_id","name", "address"]
            for field in req_fields:
                if not add_factory_payload.parse_args()[field]:
                    return {"message": f"{field} is missing."}, 400
            
            data=add_factory_payload.parse_args()
            
            if not data["name"].replace(" ", "").isalpha():
                return {"message": "Factory name should only contain alphabets."}, 400
            
            get_client= Client.get_by_id(data["client_id"]) 
            if not get_client:
                return {"message": "Client does not exist"}, 400
                
            resp= Factory.create(data)

            return {"message": "Factory added successfully", "data":{
                        "factory_id": resp.factory_id,
                        "client_id": resp.client_id,
                        "name": resp.name,
                        "address": resp.address
                        }}
            
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})


@asset_ns.route("/update_factory")
@asset_ns.doc("Update Factory")
class UpdateFactory(Resource):
    @token_required
    @asset_ns.doc("Update Factory")
    @asset_ns.expect(update_factory_payload)
    @asset_ns.response(200, "Factory updated successfully.")
    @asset_ns.response(400, "Validation Error")
    @asset_ns.doc(security=["Authorization", "Token-Type"])
    def put(self):
        try:
            req_fields = ["name", "address", "factory_id"]
            for field in req_fields:
                if not update_factory_payload.parse_args()[field]:
                    return {"message": f"{field} is missing."}, 400
            
            data=update_factory_payload.parse_args()
            print(data)
            
            
            if not data["name"].replace(" ", "").isalpha():
                return {"message": "Factory name should only contain alphabets."}, 400
            
          
            get_factory= Factory.get_by_id(data["factory_id"])
            if not get_factory:
                return {"message": "Factory does not exist"}, 400 
            
            resp= Factory.update(get_factory,data["name"],data["address"])

            return {"message": "Factory updated successfully", "data":{
                        "factory_id": resp.factory_id,
                        "name": resp.name,
                        "address": resp.address
                        }}
            
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})


@asset_ns.route("/delete_factory/<int:id>")
@asset_ns.doc("Delete Factory")
class DeleteFactory(Resource):
    @token_required
    @asset_ns.doc("Delete Factory")
    @asset_ns.response(200, "Factory deleted successfully.")
    @asset_ns.response(400, "Validation Error")
    @asset_ns.doc(security=["Authorization", "Token-Type"])
    def delete(self, id):
        try:
            if not id:
                return {"message": f"Factory ID is missing."}, 400
            
            get_factory = Factory.get_by_id(id)
            if not get_factory:
                return {"message": "Factory does not exist"}, 400
            
            Factory.delete(get_factory)
            return {"message": "Factory deleted successfully" , "success": True }, 200
            
        
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})


@asset_ns.route("/update_factory_status/<int:id>")
@asset_ns.doc("Update Factory Status")
class UpdateFactoryStatus(Resource):
    @token_required
    @asset_ns.doc("Update factory status")
    @asset_ns.response(200, "Status updated successfully.")
    @asset_ns.response(400, "Validation Error")
    @asset_ns.doc(security=["Authorization", "Token-Type"])
    def put(self,id):
        try:
            if not id:
                    return {"message": f"Factory ID is required."}, 400
            
            resp= Factory.toggle_status(id) 
            if resp is not None:
                return {
                    "message":  "Status updated successfully",
                    "success":  True,
                    "data":{
                            "id": resp.factory_id,
                            "name": resp.name,
                            "address": resp.address,
                            "created_at": resp.created_at.strftime("%Y-%m-%d"),
                            "updated_at": resp.updated_at.strftime("%Y-%m-%d"),
                            "active": resp.active
                            }
                    }, 200
            else:
                return {
                "message": "Factory does not exist" ,
                "success":  False,
                
                }, 400

        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})


@asset_ns.route("/get_factory/<int:id>")
@asset_ns.doc("Get Factory by ID")
class GetFactory(Resource):
    @user_token_required
    @asset_ns.doc("Get factory by ID")
    @asset_ns.response(200, "Factory fetched successfully.")
    @asset_ns.response(400, "Validation Error") 
    @asset_ns.doc(security=["Authorization", "Token-Type"]) 
    def get(self, id):
        try:
            if not id:
                    return {"message": f"Factory ID is required."}, 400
                
            resp = Factory.get_by_id(id) 
            if resp is not None:
                    
                return {
                    "message":  "Factory fetched successfully",
                    "success":  True,
                    "data":{
                            "id": resp.factory_id,
                            "name": resp.name,
                            "created_at": resp.created_at.strftime("%Y-%m-%d"),
                            "updated_at": resp.updated_at.strftime("%Y-%m-%d"),
                            "active": resp.active,
                            "areas": [ { 
                                    "id": x.area_id,  
                                    "area": x.name,
                                    "active": x.active if x.active else False, 
                                    "sub_area": [ {"id": sub.sub_area_id, "name": sub.name,} for sub in x.sub_areas if sub.active],
                                    "areaOwner": (next((y.user.name for y in x.users_areas if y and y.user and y.user.roles.role_id == 8),""))
                            } for x in resp.factory_areas]
                        }
                    }, 200
            else:
                return {
                "message": "Factory does not exist" ,
                "success":  False,
                
                }, 400

        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})


# USER FACTORY

@asset_ns.route("/add_user_factory")
@asset_ns.doc("Add User Factory")
class AddUserFactory(Resource):
    @token_required
    @asset_ns.doc("Add User Factory")
    @asset_ns.expect(add_user_factory_payload)
    @asset_ns.response(200, "User Factory added successfully.")
    @asset_ns.response(400, "Validation Error")
    @asset_ns.doc(security=["Authorization", "Token-Type"])
    def post(self):
        data = asset_ns.payload
        try:
            req_fields = ["user_id", "factory_id"]
            for field in req_fields:
                if not data.get(field):
                    return {"message": f"{field} is missing."}, 400

            get_user = Users.get_by_user_id(data["user_id"])
            if not get_user:
                return {"message": "User does not exist"}, 400

            get_factory = Factory.get_by_id(data["factory_id"])
            if not get_factory:
                return {"message": "Factory does not exist"}, 400

            existing_user_factory = UserFactory.get_by_user_and_factory(get_user.user_id, get_factory.factory_id)
            if existing_user_factory:
                return {"message": "User Factory already exists."}, 400

            resp = UserFactory.create(get_user.user_id, get_factory.factory_id)

            return {"message": "User Factory successfully", "data": {
                "user_id": resp.user.user_id,
                "user_email": resp.user.email,
                "factory_id": resp.factory.factory_id,
                "factory_name": resp.factory.name,
                "user_factory_id": resp.user_factory_id, 
            }}, 200
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})


@asset_ns.route("/delete_user_factory")
@asset_ns.doc("Delete User Factory")
class DeleteUserFactory(Resource):
    @token_required
    @asset_ns.doc("Delete User Factory")
    @asset_ns.expect(delete_user_factory_payload)
    @asset_ns.response(200, "User Factory deleted successfully.")
    @asset_ns.response(400, "Validation Error")
    @asset_ns.doc(security=["Authorization", "Token-Type"])
    def delete(self):
        data = asset_ns.payload
        try:
            req_fields = ["user_id", "factory_id"]
            for field in req_fields:
                if not data.get(field):
                    return {"message": f"{field} is missing."}, 400

            get_user = Users.get_by_user_id(data["user_id"])
            if not get_user:
                return {"message": "User does not exist"}, 400

            get_factory = Factory.get_by_id(data["factory_id"])
            if not get_factory:
                return {"message": "Factory does not exist"}, 400

            existing_user_factory = UserFactory.get_by_user_and_factory(get_user.user_id, get_factory.factory_id)
            if not existing_user_factory:
                return {"message": "User Factory does not exist"}, 400

            UserFactory.delete(existing_user_factory)

            return {"message": "User Factory deleted successfully", 
                    "success": True
                  }, 200
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})


@asset_ns.route("/get_user_factories/<int:user_id>")
@asset_ns.doc("Get user Factories")
class GetUserFactoryDetails(Resource):
    @user_token_required
    @asset_ns.doc("Get user Details") 
    @asset_ns.response(200, "User Factories fetched successfully.")
    @asset_ns.doc(security=["Authorization", "Token-Type"])
    def get(self,user_id):
        try:
            if not user_id:
                return {"message": f"User ID is required."}, 400

            get_user = Users.get_by_user_id(user_id)
            if not get_user:
                return {"message": "User does not exist"}, 400

            get_user_factories = UserFactory.get_by_user_id(user_id)
           
            return {"message": "User Factories fetched successfully.", 
                    "data":   [{  
                                    "factory_id": i.factory.factory_id,
                                    "name": i.factory.name,
                                    "address": i.factory.address,
                                    "created_at": i.factory.created_at.strftime("%Y-%m-%d"),
                                    "updated_at": i.factory.updated_at.strftime("%Y-%m-%d"),
                                    "active": i.factory.active,
                                    "areas": [ { 
                                    "id": x.area_id, 
                                    "area": x.name,
                                    "active": x.active if x.active else False, 
                                    "areaOwner": x.users_areas[0].user.name if x.users_areas and x.users_areas[0].user  else "",
                                         "sub_area": [ {"id": sub.sub_area_id, "name": sub.name,} for sub in x.sub_areas if sub.active]
                                         } for x in i.factory.factory_areas]
                                    }
                                for i in get_user_factories if i.factory.active
                                ]
                
                }, 200
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})
            
            

@asset_ns.route("/get_all_areas")
@asset_ns.doc("Get all Areas")
class GetAllAreas(Resource):
    # @user_token_required
    @asset_ns.doc("Get all Areas") 
    @asset_ns.response(200, "Areas fetched successfully.")
    # @asset_ns.doc(security=["Authorization", "Token-Type"])
    def get(self):

        user_subquery = (
            db.session.query(
                UserArea.area_id.label("area_id"), 
                Users.user_id.label("user_id"), 
                Users.name.label("name"), 
                Users.active.label("active")
            )
            .join(Users, Users.user_id == UserArea.user_id)  # Join to Users
            .outerjoin(UserRole, Users.user_id == UserRole.user_id)  # Join to UserRole
            .outerjoin(Role, Role.role_id == UserRole.role_id)  # Join to Role
            .filter(Users.active == True)  # Only active users
            .filter((Role.role_name == "Area") | (Role.role_name == None))  # Filter roles
            .subquery()
        ) 
        all_users = (
            db.session.query(Area,user_subquery.c.name)
            .outerjoin(user_subquery, Area.area_id == user_subquery.c.area_id)  # Join areas with the subquery
            .all()
        )
        results=[]
        for area, user_name in all_users:
            results.append({
                "area_id": area.area_id,
                "area_name": area.name,
                "area_owner": user_name if user_name else "", 
                "sub_areas": [{"id": x.sub_area_id, "name": x.name} for x in area.sub_areas if x.active],
                "factory": area.factory.name if area.factory else "",
                "factory_id": area.factory_id,
                "active": area.active,
                "created_at": area.created_at.strftime("%Y-%m-%d"),
                "updated_at": area.updated_at.strftime("%Y-%m-%d"),
            })
            print(f"User: {user_name if user_name else ''}- Area Name: {area.area_id} - {area.name}")

        return {"message": "Areas fetched successfully.", "data": results}, 200


@asset_ns.route("/add_area")
@asset_ns.doc("Add new Area")
class AddArea(Resource):
    #@token_required
    @asset_ns.doc("Add new Area")
    @asset_ns.expect(add_area_payload)
    @asset_ns.response(200, "Area added successfully.")
    @asset_ns.response(400, "Validation Error")
    #@asset_ns.doc(security=["Authorization", "Token-Type"])
    def post(self):
        try:
            req_fields = ["name", "address","factory_id"]
            for field in req_fields:
                if not add_area_payload.parse_args()[field]:
                    return {"message": f"{field} is missing."}, 400
            
            data=add_area_payload.parse_args()
            
            
            if not data["name"].replace(" ", "").isalpha():
                return {"message": "Area name should only contain alphabets."}, 400
             

            
            get_factory= Factory.get_by_id(data["factory_id"]) 
            if not get_factory:
                return {"message": "Factory does not exist"}, 400
            
            resp= Area.create(data)
        
            return {"message": "Area added successfully", "data":{
                        "area_id": resp.area_id,
                        "name": resp.name,
                        "address": resp.address,
                        "factory": get_factory.name if get_factory else "",
                        "factory_id": resp.factory_id
                        }}
               
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})


@asset_ns.route("/update_area")
@asset_ns.doc("Update Area")
class UpdateArea(Resource):
    @token_required
    @asset_ns.doc("Update Area")
    @asset_ns.expect(update_area_payload)
    @asset_ns.response(200, "Area updated successfully.")
    @asset_ns.response(400, "Validation Error")
    @asset_ns.doc(security=["Authorization", "Token-Type"])
    def put(self):
        try:
            req_fields = ["name", "address","user_id", "area_id","factory_id"]
            for field in req_fields:
                if not update_area_payload.parse_args()[field]:
                    return {"message": f"{field} is missing."}, 400
            
            data=update_area_payload.parse_args()
            print(data)

            
            get_factory= Factory.get_by_id(data["factory_id"]) 
            if not get_factory:
                return {"message": "Factory does not exist"}, 400
            
            if not data["name"].replace(" ", "").isalpha():
                return {"message": "Area name should only contain alphabets."}, 400
            
            get_area= Area.get_by_id(data["area_id"])
            if not get_area:
                return {"message": "Area does not exist"}, 400 
             
            resp= Area.update(get_area,data["name"], data["address"], data["factory_id"])

        
            return {"message": "Area updated successfully", "data":{
                        "area_id": resp.area_id,
                        "name": resp.name,
                        "address": resp.address,
                        "factory": resp.factory.name if resp.factory else "",
                        "factory_id": resp.factory_id,
                        }}
            
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})


@asset_ns.route("/delete_area/<int:id>")
@asset_ns.doc("Delete Area")
class DeleteArea(Resource):
    @token_required
    @asset_ns.doc("Delete Area")
    @asset_ns.response(200, "Area deleted successfully.")
    @asset_ns.response(400, "Validation Error")
    @asset_ns.doc(security=["Authorization", "Token-Type"])
    def delete(self, id):
        try:
            if not id:
                return {"message": f"Area ID is missing."}, 400
            
            get_area = Area.get_by_id(id)
            if not get_area:
                return {"message": "Area does not exist"}, 400
            
            Area.delete(get_area)
            return {"message": "Area deleted successfully" , "success": True }, 200
            
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})


@asset_ns.route("/update_area_status/<int:id>")
@asset_ns.doc("Update Area Status")
class UpdateAreaStatus(Resource):
    @token_required
    @asset_ns.doc("Update area status")
    @asset_ns.response(200, "Status updated successfully.")
    @asset_ns.response(400, "Validation Error")
    @asset_ns.doc(security=["Authorization", "Token-Type"])
    def put(self,id):
        try:
            if not id:
                    return {"message": f"Area ID is required."}, 400
            
            resp= Area.toggle_status(id) 
            if resp is not None:
                return {
                    "message":  "Status updated successfully",
                    "success":  True,
                    "data":{
                            "id": resp.area_id,
                            "name": resp.name,
                            "address": resp.address,
                            "created_at": resp.created_at.strftime("%Y-%m-%d"),
                            "updated_at": resp.updated_at.strftime("%Y-%m-%d"),
                            "active": resp.active
                            }
                    }, 200
            else:
                return {
                "message": "Area does not exist" ,
                "success":  False,
                
                }, 400

        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})


@asset_ns.route("/get_area/<int:id>")
@asset_ns.doc("Get Area by ID")
class GetArea(Resource):
    @user_token_required
    @asset_ns.doc("Get area by ID")
    @asset_ns.response(200, "Area fetched successfully.")
    @asset_ns.response(400, "Validation Error")
    @asset_ns.doc(security=["Authorization", "Token-Type"])
    def get(self, id):
        try:
            if not id:
                    return {"message": f"Area ID is required."}, 400
                
            resp = Area.get_by_id(id) 
            if resp is not None:
                return {
                    "message":  "Area fetched successfully",
                    "success":  True,
                    "data":{
                            "id": resp.area_id,
                            "name": resp.name,
                            "address": resp.address,
                            "factory": resp.factory.name if resp.factory else "",
                            "factory_id": resp.factory_id,
                            "created_at": resp.created_at.strftime("%Y-%m-%d"),
                            "updated_at": resp.updated_at.strftime("%Y-%m-%d"),
                            "active": resp.active,
                            }
                    }, 200
            else:
                return {
                "message": "Area does not exist" ,
                "success":  False,
                
                }, 400

        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})


# USER AREA

@asset_ns.route("/add_user_area")
@asset_ns.doc("Add User Area")
class AddUserArea(Resource):
    @token_required
    @asset_ns.doc("Add User Area")
    @asset_ns.expect(add_user_area_payload)
    @asset_ns.response(200, "User Area added successfully.")
    @asset_ns.response(400, "Validation Error")
    @asset_ns.doc(security=["Authorization", "Token-Type"])
    def post(self):
        data = asset_ns.payload
        try:
            req_fields = ["user_id", "area_id"]
            for field in req_fields:
                if not data.get(field):
                    return {"message": f"{field} is missing."}, 400

            get_user = Users.get_by_user_id(data["user_id"])
            if not get_user:
                return {"message": "User does not exist"}, 400

            get_area = Area.get_by_id(data["area_id"])
            if not get_area:
                return {"message": "Area does not exist"}, 400

            existing_user_area = UserArea.get_by_user_and_area(get_user.user_id, get_area.area_id)
            if existing_user_area:
                return {"message": "User Area already exists."}, 400

            resp = UserArea.create(get_user.user_id, get_area.area_id)

            return {"message": "User Area successfully", "data": {
                "user_id": resp.user.user_id,
                "user_email": resp.user.email,
                "area_id": resp.area.area_id,
                "area_name": resp.area.name,
                "user_area_id": resp.user_area_id, 
            }}, 200
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})


@asset_ns.route("/delete_user_area")
@asset_ns.doc("Delete User Area")
class DeleteUserArea(Resource):
    @token_required
    @asset_ns.doc("Delete User Area")
    @asset_ns.expect(delete_user_area_payload)
    @asset_ns.response(200, "User Area deleted successfully.")
    @asset_ns.response(400, "Validation Error")
    @asset_ns.doc(security=["Authorization", "Token-Type"])
    def delete(self):
        data = asset_ns.payload
        try:
            req_fields = ["user_id", "area_id"]
            for field in req_fields:
                if not data.get(field):
                    return {"message": f"{field} is missing."}, 400

            get_user = Users.get_by_user_id(data["user_id"])
            if not get_user:
                return {"message": "User does not exist"}, 400

            get_area = Area.get_by_id(data["area_id"])
            if not get_area:
                return {"message": "Area does not exist"}, 400

            existing_user_area = UserArea.get_by_user_and_area(get_user.user_id, get_area.area_id)
            if not existing_user_area:
                return {"message": "User Area does not exist"}, 400

            UserArea.delete(existing_user_area)

            return {"message": "User Area deleted successfully", 
                    "success": True
                  }, 200
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})
            

@asset_ns.route("/get_user_areas/<int:id>")
@asset_ns.doc("Get user Areas")
class GetUserAreas(Resource):
    @user_token_required
    @asset_ns.doc("Get user Details")
    @asset_ns.response(200, "User Areas fetched successfully.")
    @asset_ns.doc(security=["Authorization", "Token-Type"])
    def get(self, id):
        try:
            if not id:
                return {"message": f"User ID is required."}, 400
            
            get_user = Users.get_by_user_id(id)
            if not get_user:
                return {"message": "User does not exist"}, 400
            
            get_user_areas = UserArea.get_by_user_id(id)
            return {"message": "User Areas fetched successfully.", 
                    "data":   [{  
                                    "area_id": i.area.area_id,
                                    "name": i.area.name,
                                    "factory": i.area.factory.name if i.area.factory else "",
                                    "factory_id": i.area.factory_id,
                                    "created_at": i.area.created_at.strftime("%Y-%m-%d"),
                                    "updated_at": i.area.updated_at.strftime("%Y-%m-%d"),
                                    "active": i.area.active,
                                    }
                                for i in get_user_areas if i.area.active
                                ]
                
                }, 200
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})


@asset_ns.route("/get_sub_areas_by_area_id/<int:area_id>")
@asset_ns.doc("Get sub Areas")
class GetUserAreas(Resource):
    @user_token_required
    @asset_ns.doc("Get user Details")
    @asset_ns.response(200, "Sub Areas fetched successfully.")
    @asset_ns.doc(security=["Authorization", "Token-Type"])
    def get(self, area_id):
        try:
            if not area_id:
                return {"message": f"User ID is required."}, 400
            
            get_area= Area.get_by_id(area_id)
            if not get_area:
                return {"message": "Area does not exist"}, 400
            
            return {"message": "Sub Areas fetched successfully.", 
                    "data":   [{  
                                    "area_id": get_area.area_id,
                                    "sub_area_id": i.sub_area_id,
                                    "name": i.name,
                                    "address": i.address,
                                    "factory": get_area.factory.name if get_area.factory else "",
                                    "factory_id": get_area.factory_id,
                                    "created_at": i.created_at.strftime("%Y-%m-%d"),
                                    "updated_at": i.updated_at.strftime("%Y-%m-%d"),
                                    "active": i.active,
                                    }
                                for i in get_area.sub_areas if i.active
                                ]
                
                }, 200
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})
            
            

@asset_ns.route("/get_all_sub_areas")
@asset_ns.doc("Get all Sub Areas")
class GetAllSubAreas(Resource):
    @token_required
    @asset_ns.doc("Get all Sub Areas")
    @asset_ns.response(200, "Sub Areas fetched successfully.")
    @asset_ns.doc(security=["Authorization", "Token-Type"])
    def get(self):
        get_areas = SubArea.get_all_records()
        results = [
        {
            "sub_area_id": sub_area.sub_area_id,
            "area_id": sub_area.area.area_id,
            "name": sub_area.name,
            "address": sub_area.address,
            "created_at": sub_area.created_at.strftime("%Y-%m-%d"),
            "updated_at": sub_area.updated_at.strftime("%Y-%m-%d"),
            "active": sub_area.active,
        } for sub_area in get_areas if sub_area.active
        ]
        
        return {"message": "Sub Areas fetched successfully.", "data": results}, 200


@asset_ns.route("/add_subarea")
@asset_ns.doc("Add new Sub Area")
class AddSubArea(Resource):
    #@token_required
    @asset_ns.doc("Add new Sub Area")
    @asset_ns.expect(add_sub_area_payload)
    @asset_ns.response(200, "Sub Area added successfully.")
    @asset_ns.response(400, "Validation Error")
    #@asset_ns.doc(security=["Authorization", "Token-Type"])
    def post(self):
        try:
            req_fields = ["name", "address","area_id"]
            for field in req_fields:
                if not add_sub_area_payload.parse_args()[field]:
                    return {"message": f"{field} is missing."}, 400
            
            data=add_sub_area_payload.parse_args()
                        
            if not data["name"].replace(" ", "").isalpha():
                return {"message": "Sub Area name should only contain alphabets."}, 400
            
           
            get_area= Area.get_by_id(data["area_id"]) 
            print("Area: ", get_area)
            if not get_area:
                return {"message": "Area does not exist"}, 400 
            
           
            resp= SubArea.create(data)
        
            return {"message": "Sub Area added successfully", "data":{
                        "sub_area_id": resp.sub_area_id,
                        "area_id": resp.area_id,
                        "name": resp.name,
                        "address": resp.address,
                        }}
                
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})


@asset_ns.route("/add_multiple_sub_areas")
@asset_ns.doc("Add multiple Sub Area")
class AddMultipleSubArea(Resource):
    @token_required
    @asset_ns.doc("Add multiple Sub Area")
    @asset_ns.expect(add_multiple_sub_areas_payload)
    @asset_ns.response(200, "Sub Areas added successfully.")
    @asset_ns.response(400, "Validation Error")
    @asset_ns.doc(security=["Authorization", "Token-Type"])
    def post(self):
        data=asset_ns.payload
        try:
            req_fields = ["areas"]
            for field in req_fields:
                if field not in data and not data[field]:
                    return {"message": f"{field} is missing."}, 400

            given_areas=[x["area_id"] for x in data["areas"]]
            verify_areas= Area.get_records_by_list(given_areas)

            if len(verify_areas) != len(given_areas): 
                return {"message": "Some Areas does not exist"}, 400 
                     
            resp= SubArea.create_list(data)
        
            return {"message": "Sub Area added successfully", 
                    "success": resp}
               
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})



@asset_ns.route("/update_sub_area")
@asset_ns.doc("Update Sub Area")
class UpdateSubArea(Resource):
    @token_required
    @asset_ns.doc("Update Sub Area")
    @asset_ns.expect(update_sub_area_payload)
    @asset_ns.response(200, "Sub Area updated successfully.")
    @asset_ns.response(400, "Validation Error")
    @asset_ns.doc(security=["Authorization", "Token-Type"])
    def put(self):
        try:
            req_fields = ["name", "address","sub_area_id"]
            for field in req_fields:
                if not update_sub_area_payload.parse_args()[field]:
                    return {"message": f"{field} is missing."}, 400
            
            data=update_sub_area_payload.parse_args()
            print(data)
            
            if not data["name"].replace(" ", "").isalpha():
                return {"message": "Sub Area name should only contain alphabets."}, 400
            
            if "file" not in data:
                return {"message": "file field is required"}, 400
            
            
            get_area= SubArea.get_by_id(data["sub_area_id"])
            if not get_area:
                return {"message": "Sub Area does not exist"}, 400 
            
                 
            resp= SubArea.update(get_area,data["name"], data["address"])
        
            return {"message": "Area updated successfully", "data":{
                        "sub_area_id": resp.sub_area_id,
                        "area_id": resp.area.area_id,
                        "name": resp.name,
                        "address": resp.address,
                        }}
            
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})


@asset_ns.route("/delete_sub_area/<int:id>")
@asset_ns.doc("Delete Sub Area")
class DeleteSubArea(Resource):
    @token_required
    @asset_ns.doc("Delete Sub Area")
    @asset_ns.response(200, "Sub Area deleted successfully.")
    @asset_ns.response(400, "Validation Error")
    @asset_ns.doc(security=["Authorization", "Token-Type"])
    def delete(self, id):
        try:
            if not id:
                return {"message": f"Sub Area ID is missing."}, 400
            
            get_area = SubArea.get_by_id(id)
            if not get_area:
                return {"message": "Sub Area does not exist"}, 400
            
            SubArea.delete(get_area)
            return {"message": "Sub Area deleted successfully" , "success": True }, 200
            
        
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})


@asset_ns.route("/update_sub_area_status/<int:id>")
@asset_ns.doc("Update Sub Area Status")
class UpdateSubAreaStatus(Resource):
    @token_required
    @asset_ns.doc("Update sub area status")
    @asset_ns.response(200, "Status updated successfully.")
    @asset_ns.response(400, "Validation Error")
    @asset_ns.doc(security=["Authorization", "Token-Type"])
    def put(self,id):
        try:
            if not id:
                    return {"message": f"Sub Area ID is required."}, 400
            
            resp= SubArea.toggle_status(id) 
            if resp is not None:
                return {
                    "message":  "Status updated successfully",
                    "success":  True,
                    "data":{
                            "id": resp.sub_area_id,
                            "area_id": resp.area.area_id,
                            "name": resp.name,
                            "logo": resp.logo,
                            "address": resp.address,
                            "created_at": resp.created_at.strftime("%Y-%m-%d"),
                            "updated_at": resp.updated_at.strftime("%Y-%m-%d"),
                            "active": resp.active
                            }
                    }, 200
            else:
                return {
                "message": "Sub Area does not exist" ,
                "success":  False,
                
                }, 400

        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})


@asset_ns.route("/get_sub_area/<int:id>")
@asset_ns.doc("Get Sub Area by ID")
class GetSubArea(Resource):
    @token_required
    @asset_ns.doc("Get sub area by ID")
    @asset_ns.response(200, "Sub Area fetched successfully.")
    @asset_ns.response(400, "Validation Error")
    @asset_ns.doc(security=["Authorization", "Token-Type"])
    def get(self, id):
        try:
            if not id:
                    return {"message": f"Sub Area ID is required."}, 400
                
            resp = SubArea.get_by_id(id) 
            if resp is not None:
                return {
                    "message":  "Sub Area fetched successfully",
                    "success":  True,
                    "data":{
                            "id": resp.area_id,
                            "area_id": resp.area.area_id if resp.area else None,
                            "name": resp.name,
                            "logo": resp.logo,
                            "address": resp.address,
                            "created_at": resp.created_at.strftime("%Y-%m-%d"),
                            "updated_at": resp.updated_at.strftime("%Y-%m-%d"),
                            "active": resp.active,
                            }
                    }, 200
            else:
                return {
                "message": "Sub Area does not exist" ,
                "success":  False,
                
                }, 400

        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})
            
            
            
@asset_ns.route("/add_camera")
@asset_ns.doc("Add new Camera")
class AddNewCamera(Resource):
    #@token_required 
    @asset_ns.doc("Add new Camera")
    @asset_ns.expect(add_camera_payload)
    @asset_ns.response(200, "Camera added successfully.") 
    @asset_ns.response(400, "Validation Error")
    #@asset_ns.doc(security=["Authorization", "Token-Type"])
    def post(self):
        data=asset_ns.payload
        try:
            req_fields = ["client_id","factory_id","area_id","sub_area_id","camera_id", "camera_ip", "modules", 
                          "camera_name", "camera_position_no", "nvr_no", 
                          "username", "password", "stream", "port"]
            for field in req_fields:
                if field not in data and not data[field]:
                    return {"message": f"{field} is missing."}, 400
            
            get_client= Client.get_by_id(data["client_id"]) 
            if not get_client:
                return {"message": "Client does not exist"}, 400
                
            get_factory= Factory.get_by_id(data["factory_id"]) 
            if not get_factory:
                return {"message": "Factory does not exist"}, 400
                
            get_area= Area.get_by_id(data["area_id"]) 
            if not get_area:
                return {"message": "Area does not exist"}, 400
                
            get_sub_area= SubArea.get_by_id(data["sub_area_id"]) 
            if not get_sub_area:
                return {"message": "Sub area does not exist"}, 400
            
            get_camera= Cameras.get_by_id(data["camera_id"])
            if get_camera:
                return {"message": "Camera ID already exist", "Camera ID": data["camera_id"]}, 200
        
            resp = asset_func.add_new_camera(data)

            return {"message": resp["message"], 
                "success": resp["success"],
                "data": resp["data"] if "data" in resp else None
            },200
                   
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})
            

            
@asset_ns.route("/delete_camera/<string:camera_id>")
@asset_ns.doc("Delete Camera")
class DeleteCamera(Resource):
    # @token_required
    @asset_ns.doc("Delete Camera")
    @asset_ns.response(200, "Camera deleted successfully.")
    @asset_ns.response(400, "Validation Error")
    # @asset_ns.doc(security=["Authorization", "Token-Type"])
    def delete(self, camera_id):
        try:
            if not camera_id:
                return {"message": f"Camera ID is missing."}, 400
            
            get_camera = Cameras.get_by_id(camera_id)
            if not get_camera:
                return {"message": "Camera does not exist"}, 400
            
            Cameras.delete(get_camera)
            return {"message": "Camera deleted successfully", "success": True}, 200
            
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"}) 


@asset_ns.route("/update_camera_status/<string:camera_id>")
@asset_ns.doc("Update Camera Status")
class UpdateCameraStatus(Resource):
    # @token_required
    @asset_ns.doc("Update camera status")
    @asset_ns.response(200, "Status updated successfully.")
    @asset_ns.response(400, "Validation Error")
    # @asset_ns.doc(security=["Authorization", "Token-Type"])
    def put(self, camera_id):
        try:
            if not camera_id:
                return {"message": f"Camera ID is required."}, 400

            resp = Cameras.toggle_status(camera_id)
            if resp is not None:
                return {
                    "message": "Camera status updated successfully",
                    "success": True,
                    "data": {
                        "id": resp.camera_id,
                        "active": resp.active,
                    },
                }, 200
            else:
                return {
                    "message": "Camera does not exist",
                    "success": False,
                }, 400

        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})
            
            
@asset_ns.route("/get_cameras")
@asset_ns.doc("Get Cameras")
class GetCameras(Resource):
    # @token_required
    @asset_ns.expect(get_camera_payload)
    @asset_ns.response(200, "Cameras fetched successfully.")
    @asset_ns.response(400, "Validation Error")
    # @asset_ns.doc(security=["Authorization", "Token-Type"])
    def put(self):
        data=asset_ns.payload
        try:
            req_fields = ["client_id","factory_id"]
            for field in req_fields:
                if field not in data and not data[field]:
                    return {"message": f"{field} is missing."}, 400
            
            get_client= Client.get_by_id(data["client_id"]) 
            if not get_client:
                return {"message": "Client does not exist"}, 400
                
            get_factory= Factory.get_by_id(data["factory_id"]) 
            if not get_factory:
                return {"message": "Factory does not exist"}, 400
                
  
            cameras = asset_func.get_cameras(data)

            
            return {
                "message": "Cameras fetched successfully" if cameras else "No cameras found",
                "success": True,
                "data": cameras
            }, 200

        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})


@asset_ns.route("/upload_camera_livefeed")
@asset_ns.doc("Upload Camera Livefeed")
class CreateUpdateCameraLivefeed(Resource):
    # @token_required
    @asset_ns.expect(create_update_camera_livefeed_payload)
    @asset_ns.response(200, "Camera Livefeed uploaded successfully.")
    @asset_ns.response(400, "Validation Error")
# @asset_ns.doc(security=["Authorization", "Token-Type"])
    def put(self):
        data=asset_ns.payload
        try:
            req_fields = ["data"]
            for field in req_fields:
                if field not in data and not data[field]:
                    return {"message": f"{field} is missing."}, 400
            
            CamerasliveFeed.create_update(data["data"])
            
            return {
                "message": "Camera Livefeed uploaded successfully",
                "success": True,
            }, 200

        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})


@asset_ns.route("/get_camera_livefeed")
@asset_ns.doc("Get Camera Livefeed")
class GetCameraLivefeed(Resource):
    # @token_required
    @asset_ns.expect(get_camera_livefeed_payload)
    @asset_ns.response(200, "Camera Livefeed fetched successfully.")
    @asset_ns.response(400, "Validation Error")
    # @asset_ns.doc(security=["Authorization", "Token-Type"])
    def put(self):
        data = asset_ns.payload
        try:
            req_fields = ["camera_id", "client_id", "factory_id"]
            for field in req_fields:
                if field not in data and not data[field]:
                    return {"message": f"{field} is missing."}, 400
            
            livefeed = CamerasliveFeed.get_by_camera_id(data["camera_id"], data["client_id"], data["factory_id"])
            if livefeed:
                return {
                    "message": "Camera Livefeed fetched successfully",
                    "success": True,
                    "data": {
                        "camera_id": livefeed.camera_id,
                        "client_id": livefeed.client_id,
                        "client_name": Client.get_name_by_id(livefeed.client_id),
                        "factory_id": livefeed.factory_id,
                        "factory_name": Factory.get_name_by_id(livefeed.factory_id),
                        "image_url": livefeed.image_url,
                        "timestamp": livefeed.timestamp.strftime("%Y-%m-%d %H:%M:%S"),
                    }
                }, 200
            
            else:
                return {
                    "message": "Camera Livefeed does not exist",
                    "success": False,
                }, 400

        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"}) 