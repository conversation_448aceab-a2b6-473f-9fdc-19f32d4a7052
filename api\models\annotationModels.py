from api import db
import datetime
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import ARRAY
import datetime
from sqlalchemy import and_, or_
    
class AnnotationCameraImages(db.Model):
    __tablename__ = 'ann_camera_images'
    __table_args__ = (
        db.Index('idx_camera_client_factory_batch', 'client_id', 'factory_id', 'batch_id'),
        db.Index('idx_camera_id', 'camera_id'), 
    )
    id = db.Column(db.Integer, primary_key=True)
    camera_id = db.Column(db.Text, db.ForeignKey('ast_cameras.camera_id'))
    client_id = db.Column(db.Integer, db.ForeignKey('ast_client.client_id'))
    factory_id = db.Column(db.Integer, db.ForeignKey('ast_factory.factory_id'))
    batch_id = db.Column(db.Integer, nullable=False, default=1)
    image_url = db.Column(db.Text, nullable=False)
    
    annotation = db.Column(db.String(1024), nullable=False, default="")
    annotation_user_id = db.Column(db.Integer, nullable=False, default=0)
    review_user_id = db.Column(db.Integer, nullable=False, default=0)
    annotation_status = db.Column(db.Boolean, default=False, nullable=False)
    review_status = db.Column(db.Boolean, default=False, nullable=False)
    rejected = db.Column(db.Boolean, default=False, nullable=False)
    review_comments = db.Column(db.Text, nullable=False, default="")
    disqualified = db.Column(db.Boolean, default=False, nullable=False)
    background_image = db.Column(db.Boolean, default=False, nullable=False)
    
    detection_model = db.Column(db.Text, nullable=False, default="yolo")
    dataset_id = db.Column(db.Integer, nullable=False, default=0)
    job_id = db.Column(db.Integer, nullable=True) 
    
    created_at = db.Column(db.DateTime, default=datetime.datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    
    @classmethod
    def get_by_id(cls, id):
        return cls.query.filter_by(id=id).first()
    
    @classmethod
    def get_by_camera_id(cls, camera_id):
        return cls.query.filter_by(camera_id=camera_id).first()
    
    @classmethod
    def get_all_records(cls):
        return cls.query.all()
    
    @classmethod
    def update_annotation(cls, annotations):

        # Convert list of dicts to a bulk update format
        updates = [
            {
              "id": item["id"], 
              "annotation_status": True if item["disqualified"] == False else False,
              "annotation": item["annotation"],
              "disqualified": item["disqualified"],
              "review_status": False,
              "rejected": False,
              "review_comments": "",
              "updated_at": datetime.datetime.utcnow()
             }
            for item in annotations
        ]

        # Perform bulk update
        db.session.bulk_update_mappings(cls, updates)
        db.session.commit()

    @classmethod
    def update_review(cls, reviews):
 
        # Convert list of dicts to a bulk update format
        updates = [
            {
              "id": item["id"], 
              "review_status": True,
              "rejected": item["rejected"],
              "review_comments": item["review_comments"],
              "annotation_status": False if item["rejected"] == True else True,
              "updated_at": datetime.datetime.utcnow()
             }
            for item in reviews
        ]

        # Perform bulk update
        db.session.bulk_update_mappings(cls, updates)
        db.session.commit()
    
    @classmethod
    def bulk_update(cls, data):
        if data:
            db.session.bulk_update_mappings(cls, data)
            db.session.commit()
        
    @classmethod
    def add_multiple_rows(cls,dataset):
        rows = []

        for data in dataset:
            image_url=data.get("image_url")
            #Hack for Unilever
            if data.get("client_id") == 1:
                image_url = f"https://unilever-alert-images.s3.amazonaws.com/{image_url}"
            
            new_record = cls(
                camera_id=data.get("camera_id"),
                client_id=data.get("client_id"),
                factory_id=data.get("factory_id"),
                batch_id=data.get("batch_id") or 1,
                image_url=image_url,
                detection_model=data.get("detection_model"),
                background_image=data.get("background_image") or False,
                annotation="",
                annotation_user_id=0,
                review_user_id=0,
                annotation_status = False,
                review_status = False,
                rejected = False,
                review_comments = "",
                disqualified = False,
                dataset_id = 0,
                created_at=datetime.datetime.utcnow(),
                updated_at=datetime.datetime.utcnow()
            )
            rows.append(new_record)

        if rows:
            db.session.add_all(rows)
            db.session.commit()
            return True
        return False
        
    def delete(self):
            db.session.delete(self)
            db.session.commit()
            

class AnnotationDataset(db.Model):
    __tablename__ = 'ann_dataset'
    __table_args__ = (
        db.Index('idx_dataset_client_factory_batch', 'client_id', 'factory_id', 'batch_id'),
    )
    dataset_id = db.Column(db.Integer, primary_key=True)
    client_id = db.Column(db.Integer, db.ForeignKey('ast_client.client_id'))
    factory_id = db.Column(db.Integer, db.ForeignKey('ast_factory.factory_id'))
    batch_id = db.Column(db.Integer, nullable=False, default=1)
    name = db.Column(db.Text, nullable=False)
    dataset_url = db.Column(db.Text, nullable=False)
    dataset_size = db.Column(db.Text, nullable=False)
    images = db.Column(db.Integer, default=0)
    description = db.Column(db.String(1024), nullable=False, default="")
    created_at = db.Column(db.DateTime, default=datetime.datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    
    @classmethod
    def get_by_id(cls, dataset_id):
        return cls.query.filter_by(dataset_id=dataset_id).first() 

    @classmethod
    def get_all_records(cls):
        return cls.query.all()
        
    @classmethod
    def get_records_by_client_id(cls, client_id):
        return cls.query.filter_by(client_id=client_id).all()
        
    @classmethod
    def get_records_by_factory_id(cls, factory_id):
        return cls.query.filter_by(factory_id=factory_id).all()
        
    @classmethod
    def create(cls, data):
        new_rec = cls(
            client_id=data["client_id"],
            factory_id=data["factory_id"],
            batch_id=data["batch_id"],
            name=data["name"],
            images=data["images"],
            dataset_url=data["dataset_url"],
            dataset_size=data["dataset_size"],
            description=data["description"],
            created_at=datetime.datetime.utcnow(),
            updated_at=datetime.datetime.utcnow()
        )
        db.session.add(new_rec)
        db.session.commit()
        return new_rec
        
        
class AnnotationYoloClasses(db.Model):
    __tablename__ = 'ann_yolo_classes'
    yolo_classes_id = db.Column(db.Integer, primary_key=True)
    client_id = db.Column(db.Integer, db.ForeignKey('ast_client.client_id'))
    factory_id = db.Column(db.Integer, db.ForeignKey('ast_factory.factory_id'))
    batch_id = db.Column(db.Integer, nullable=False, default=1)
    modules = db.Column(ARRAY(db.Integer))
    created_at = db.Column(db.DateTime, default=datetime.datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    
    @classmethod
    def get_all_records(cls):
        return cls.query.all()
        
    @classmethod
    def get_record_by_client_factory_id(cls, client_id, factory_id):
        return cls.query.filter_by(client_id=client_id, factory_id=factory_id).first()
        
    @classmethod
    def get_record_by_client_factory_batch_id(cls, client_id, factory_id, batch_id):
        return cls.query.filter_by(client_id=client_id, factory_id=factory_id, batch_id=batch_id).first()
    
    @classmethod
    def get_modules(cls, client_id, factory_id, batch_id):
        result = cls.query.with_entities(cls.modules).filter_by(client_id=client_id, factory_id=factory_id, batch_id=batch_id).first()
        modules_list = []
        if result:
            modules_list = result.modules
            modules_list.sort

        return modules_list
        
    @classmethod
    def create_update(cls, data):

        client_id=data["client_id"]
        factory_id=data["factory_id"]
        modules=data["modules"]
        batch_id=data["batch_id"]

        modules = sorted(modules)

        object_exists=cls.query.filter_by(client_id=client_id,factory_id=factory_id,batch_id=batch_id).first()

        if object_exists:
            object_exists.modules=modules
            object_exists.batch_id=batch_id
            object_exists.updated_at=datetime.datetime.utcnow()
            db.session.commit()
        else:    
            # Create a new record if it doesn't exist
            new_rec = cls(
                modules=modules,
                batch_id=batch_id,
                client_id=client_id,
                factory_id=factory_id,
                created_at=datetime.datetime.utcnow(),
                updated_at=datetime.datetime.utcnow()
            )
            db.session.add(new_rec)
            db.session.commit()


class AnnotationJobAssignment(db.Model):
    __tablename__ = 'ann_job_assignment'
    job_id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('uam_user.user_id'))
    client_id = db.Column(db.Integer, db.ForeignKey('ast_client.client_id'))
    factory_id = db.Column(db.Integer, db.ForeignKey('ast_factory.factory_id'))
    batch_id = db.Column(db.Integer, nullable=False, default=1)
    limit = db.Column(db.Integer, nullable=False, default=100)
    status = db.Column(db.Text, nullable=False, default="pending")
    created_at = db.Column(db.DateTime, default=datetime.datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    
    @classmethod
    def get_all_records(cls):
        return cls.query.all()
        
    @classmethod
    def get_record_by_client_factory_id(cls, client_id, factory_id):
        return cls.query.filter_by(client_id=client_id, factory_id=factory_id).first()
        
    @classmethod
    def get_record_by_client_factory_batch_id(cls, client_id, factory_id, batch_id):
        return cls.query.filter_by(client_id=client_id, factory_id=factory_id, batch_id=batch_id).first()
    
    @classmethod
    def get_record_by_id(cls, job_id):
        return cls.query.filter_by(job_id=job_id).first()
    
    @classmethod
    def get_records_by_user_id(cls, user_id):
        return cls.query.filter_by(user_id=user_id).all()
    
        
    @classmethod
    def create(cls, data):
        user_id = data["user_id"]
        client_id = data["client_id"]
        factory_id = data["factory_id"]
        batch_id = data["batch_id"]
        limit = data["limit"]

        try:
            # Fetch unannotated images first
            unassigned_images = db.session.query(AnnotationCameraImages).filter(
                and_(
                    AnnotationCameraImages.client_id == client_id,
                    AnnotationCameraImages.factory_id == factory_id,
                    AnnotationCameraImages.batch_id == batch_id,
                    AnnotationCameraImages.annotation_status == False,
                    AnnotationCameraImages.disqualified == False,
                    AnnotationCameraImages.background_image == False,
                    AnnotationCameraImages.job_id == None,
                    AnnotationCameraImages.annotation_user_id == 0
                )
            ).limit(limit).all()
            
            # print("unassigned_images:", len(unassigned_images))

            # If no unassigned images, raise an exception
            if not unassigned_images:
                raise Exception("No unannotated images available for assignment.")

            # Create a new job assignment record
            new_rec = cls(
                user_id=user_id,
                batch_id=batch_id,
                client_id=client_id,
                factory_id=factory_id,
                limit=limit,
                status="pending",
                created_at=datetime.datetime.utcnow(),
                updated_at=datetime.datetime.utcnow()
            )
            db.session.add(new_rec)
            db.session.commit()

            update_images = [
                {"id": img.id, "job_id": new_rec.job_id, "annotation_user_id": user_id}
                for img in unassigned_images
            ]
            AnnotationCameraImages.bulk_update(update_images)
            return new_rec

        except Exception as e:
            db.session.rollback()
            raise Exception(str(e))

    @classmethod
    def delete(cls, job):
        db.session.delete(job)
        db.session.commit()
