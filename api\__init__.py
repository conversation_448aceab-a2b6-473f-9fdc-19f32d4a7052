# """ 
#     <AUTHOR>
#     @abstract app initilize
#     @since 06-09-2023
# """
from flask import Flask
from flask_restx import Api, Namespace
from flask_cors import CORS
from flask_sqlalchemy import SQLAlchemy
from configparser import Config<PERSON><PERSON>er
from flask_mqtt import Mqtt
import os

api = Api(
    title='UAM',
    version='1.0',
    description='''
The UAM Backend API provides a set of endpoints for managing the different dashboards. This API enables users to interact with the UAM system.

## Key Features

- **User Authentication:** Secure endpoints with user authentication to ensure authorized access to sensitive information and control functionalities.

## Authentication

To access certain endpoints, users must authenticate using API keys or other secure methods to ensure data security and prevent unauthorized access.

''',
    # All API metadatas
)

authorizations = {
    'Authorization': {
        "type": "apiKey",
        "in": "header",
        "name": "Authorization"
    },
    'Token-Type': {
        "type": "apiKey",
        "in": "header",
        "name": "Token-Type"
    },
    
}


uam_ns = Namespace('UAM', description='Get information about User Access Management', authorizations=authorizations)
asset_ns = Namespace('Asset', description='Get information about Asset stored in the database', authorizations=authorizations)
annotation_ns = Namespace('Annotation', description='Get information about annotation stored in the database', authorizations=authorizations)
hse_ns = Namespace('HSE', description='Get information about HSE stored in the database', authorizations=authorizations)
import_ns = Namespace('Import', description='Import entities from other projects', authorizations=authorizations)
surveillance_ns = Namespace('Surveillance', description='Get information about Surveillance stored in the database', authorizations=authorizations)
inspection_ns = Namespace('Inspection', description='Computer Vision Inspection', authorizations=authorizations)
facial_ns = Namespace('Facial', description='Computer Vision Facial Recognition', authorizations=authorizations)
report_ns = Namespace('Report', description='Report Generation', authorizations=authorizations)

api.add_namespace(uam_ns, '/uam')
api.add_namespace(asset_ns, '/asset')
api.add_namespace(annotation_ns, '/annotation')
api.add_namespace(hse_ns, '/hse')
api.add_namespace(import_ns, '/import')
api.add_namespace(surveillance_ns, '/surveillance')
api.add_namespace(inspection_ns, '/inspection')
api.add_namespace(facial_ns, '/facial')
api.add_namespace(report_ns, '/report')

env_application_type_key = "APPLICATION_TYPE"
env_application_type_value = "HOST"
if env_application_type_key in os.environ:
    env_application_type_value = os.environ[env_application_type_key]
    print(f"{env_application_type_key} exists with value: {env_application_type_value}")

    
if env_application_type_value == "CONTAINERISED":
    app = Flask(__name__,static_url_path="/usr/local/disruptlabs/uam_backend/api/static")
else:
    app = Flask(__name__,static_url_path="/api/static")

api.init_app(app)

app.config.SWAGGER_UI_DOC_EXPANSION = 'list'

CORS(app,resources={r"*": {"origins": "*"}})
app.config['CORS_HEADERS'] = 'Content-Type'

config_parser = ConfigParser()
if env_application_type_value == "CONTAINERISED":
    config_parser.read('/usr/local/disruptlabs/uam_backend/config.cfg')
else:
    config_parser.read('config.cfg')


app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['SQLALCHEMY_DATABASE_URI'] = config_parser.get('global', 'SQLALCHEMY_DATABASE_URI')
#app.config['SQLALCHEMY_ECHO'] = True # Enable query logging
app.config["SECRET_KEY"]= config_parser.get('global', 'SECRET_KEY')
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16 MB

app.config["smtp_server"] = config_parser.get('smtp', 'smtp_server')
app.config["smtp_port"] = int(config_parser.get('smtp', 'smtp_port'))
app.config["smtp_username"] = config_parser.get('smtp', 'smtp_username')
app.config["smtp_password"] = config_parser.get('smtp', 'smtp_password')


db = SQLAlchemy(app)


# MQTT Configuration
app.config['MQTT_BROKER_URL'] = config_parser.get('mqtt', 'MQTT_BROKER_HOST')
app.config['MQTT_BROKER_PORT'] = int(config_parser.get('mqtt', 'MQTT_BROKER_PORT'))
app.config['MQTT_USERNAME'] = config_parser.get('mqtt', 'MQTT_USERNAME')
app.config['MQTT_PASSWORD'] = config_parser.get('mqtt', 'MQTT_PASSWORD')
app.config['MQTT_KEEPALIVE'] = 60
app.config['MQTT_TLS_ENABLED'] =  False  # Set to True if using TLS/SSL

# Initialize MQTT client
mqtt_client = Mqtt(app)


# Handle MQTT connection event
@mqtt_client.on_connect()
def handle_connect(client, userdata, flags, rc):
    if rc == 0:
        print("Connected to MQTT Broker!")
    else:
        print(f"Failed to connect, return code {rc}")
        
        
import api.routes
import api.models 

 

