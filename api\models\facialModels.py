#/*
# *
# * This document contains information that is confidential and proprietary to Siemens
# * Corporation. This information is supplied for identification, maintenance, evaluation, engineering,
# * and inspection purposes only, and shall not be duplicated or disclosed without prior written
# * permission from an authorized representative of Siemens. This document and any other
# * confidential information shall not be released to any third party without a valid Confidential
# * Information Exchange agreement signed by the third party and an authorized Siemens
# * representative. In accepting this document, the recipient agrees to make every reasonable effort
# * to prevent the unauthorized use of this information.
# *
#*/


from api import db
import datetime
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import ARRAY
import datetime
from sqlalchemy import desc, func

class FacialEmployees(db.Model):
    __tablename__ = 'facial_employees'
    __table_args__ = (
        db.Index('idx_facial_employee_client_factory_employee', 'client_id', 'factory_id', 'employee_id'),
    )
    employee_id = db.Column(db.Text, primary_key=True)
    name = db.Column(db.Text, nullable=False)
    designation = db.Column(db.Text, nullable=True)
    department = db.Column(db.Text, nullable=True)
    blacklisted = db.Column(db.Bo<PERSON>, default=False, nullable=True)
    client_id = db.Column(db.Integer, db.ForeignKey('ast_client.client_id'))
    factory_id = db.Column(db.Integer, db.ForeignKey('ast_factory.factory_id'))
    created_at = db.Column(db.DateTime, default=datetime.datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    local_db_id = db.Column(db.Integer)
    
    @classmethod
    def get_by_employee_id(cls, employee_id):
        return cls.query.filter_by(employee_id=employee_id).first()
    
    @classmethod
    def get_records_by_client_id(cls, client_id):
        return cls.query.filter_by(client_id=client_id).all()
        
    @classmethod
    def get_records_by_factory_id(cls, factory_id):
        return cls.query.filter_by(factory_id=factory_id).all()
        
    @classmethod
    def get_records_by_client_factory_id(cls, client_id, factory_id):
        return cls.query.filter_by(client_id=client_id, factory_id=factory_id).all()
        
    @classmethod
    def get_all_records(cls):
        return cls.query.all()
        
    @classmethod
    def get_local_db_id(cls,client_id,factory_id):
        latest_row= cls.query.filter_by(client_id=client_id, factory_id=factory_id).order_by(desc(cls.local_db_id)).first()
        return latest_row.local_db_id if latest_row else None
        
    @classmethod
    def create(cls, data):
        new_rec = cls(
            local_db_id=data["id"],
            employee_id=data["employee_id"],
            client_id=data["client_id"],
            factory_id=data["factory_id"],
            name=data["name"],
            designation=data["designation"],
            blacklisted=data["blacklisted"],
            created_at=datetime.datetime.utcnow(),
            updated_at=datetime.datetime.utcnow()
        )
        db.session.add(new_rec)
        db.session.commit()
        return new_rec
        
    @classmethod
    def create_update(cls, data):

        record_exists=cls.query.filter_by(client_id=data["client_id"],factory_id=data["factory_id"],employee_id=data["employee_id"]).first()

        if record_exists:
            record_exists.name=data.get("name", None)
            record_exists.designation=data.get("designation", None)
            record_exists.department=data.get("department", None)
            record_exists.blacklisted=data.get("blacklisted", None)
            record_exists.updated_at=datetime.datetime.utcnow()
            db.session.commit()
        else:    
            # Create a new record if it doesn't exist
            new_rec = cls(
                local_db_id=item.get("id"),
                employee_id=data.get("employee_id"),
                client_id=data.get("client_id"),
                factory_id=data.get("factory_id"),
                name=data.get("name", None),
                designation=data.get("designation", None),
                department=data.get("department", None),
                blacklisted=data.get("blacklisted", None),
                created_at=datetime.datetime.utcnow(),
                updated_at=datetime.datetime.utcnow()
            )
            db.session.add(new_rec)
            db.session.commit()
            
    @classmethod
    def add_multiple_rows(cls, data):
        rows = []
        for item in data:
            record_exists=cls.query.filter_by(client_id=item["client_id"],factory_id=item["factory_id"],employee_id=item["employee_id"]).first()

            if record_exists:
                record_exists.name=data.get("name", None)
                record_exists.designation=item.get("designation", None)
                record_exists.department=item.get("department", None)
                record_exists.blacklisted=item.get("blacklisted", None)
                record_exists.updated_at=datetime.datetime.utcnow()
                db.session.commit()
                    
            else:
                new_record = cls(
                    local_db_id=item.get("id"),
                    employee_id=item.get("employee_id"),
                    client_id=item.get("client_id"),
                    factory_id=item.get("factory_id"),
                    name=item.get("name", None),
                    designation=item.get("designation", None),
                    department=item.get("department", None),
                    blacklisted=item.get("blacklisted", None),
                    created_at=datetime.datetime.utcnow(),
                    updated_at=datetime.datetime.utcnow()
                )
                rows.append(new_record)

        if rows:
            db.session.add_all(rows)
            db.session.commit()
        return True
    
class FacialEmployeeEmbeddings(db.Model):
    __tablename__ = 'facial_employee_embeddings'
    __table_args__ = (
        db.Index('idx_facial_employee_embeddings_client_factory_employee', 'client_id', 'factory_id', 'employee_id'),
    )
    embedding_id = db.Column(db.Integer, primary_key=True)
    client_id = db.Column(db.Integer, db.ForeignKey('ast_client.client_id'))
    factory_id = db.Column(db.Integer, db.ForeignKey('ast_factory.factory_id'))
    embedding_vector =  db.Column(db.Text, nullable=False)
    employee_id = db.Column(db.Text, db.ForeignKey('facial_employees.employee_id'))
    created_at = db.Column(db.DateTime, default=datetime.datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    local_db_id = db.Column(db.Integer)
    
    @classmethod
    def get_by_embedding_id(cls, embedding_id):
        return cls.query.filter_by(embedding_id=embedding_id).first()
    
    @classmethod
    def get_records_by_employee_id(cls, employee_id):
        return cls.query.filter_by(employee_id=employee_id).all()
        
    @classmethod
    def get_records_by_client_factory_id(cls, client_id, factory_id):
        return cls.query.filter_by(client_id=client_id, factory_id=factory_id).all()
        
    @classmethod
    def get_all_records(cls):
        return cls.query.all()
        
    @classmethod
    def get_local_db_id(cls,client_id,factory_id):
        latest_row= cls.query.filter_by(client_id=client_id, factory_id=factory_id).order_by(desc(cls.local_db_id)).first()
        return latest_row.local_db_id if latest_row else None
        
    @classmethod
    def create(cls, data):
        new_rec = cls(
            local_db_id=data["id"],
            client_id=data["client_id"],
            factory_id=data["factory_id"],
            employee_id=data["employee_id"],
            embedding_vector=data["embedding_vector"],
            created_at=datetime.datetime.utcnow(),
            updated_at=datetime.datetime.utcnow()
        )
        db.session.add(new_rec)
        db.session.commit()
        return new_rec
    
    
    @classmethod
    def add_multiple_rows(cls, data):
        rows = []
        for item in data:
            new_record = cls(
                local_db_id=item["id"],
                client_id=item["client_id"],
                factory_id=item["factory_id"],
                employee_id=item.get("employee_id"),
                embedding_vector=item.get("embedding_vector"),
                created_at=datetime.datetime.utcnow(),
                updated_at=datetime.datetime.utcnow()
            )
            rows.append(new_record)

        if rows:
            db.session.add_all(rows)
            db.session.commit()
        return True
    
class FacialRecognitionLogs(db.Model):
    __tablename__ = 'facial_recognition_logs'
    __table_args__ = (
        db.Index('idx_facial_recognition_client_factory_local_db_log_id', 'client_id', 'factory_id', 'local_db_log_id'),
        db.Index('idx_facial_recognition_employee_id', 'employee_id'), 
    )
    log_id = db.Column(db.Integer, primary_key=True)
    local_db_log_id = db.Column(db.Integer)
    camera_id = db.Column(db.Text, db.ForeignKey('ast_cameras.camera_id'))
    employee_id = db.Column(db.Text, db.ForeignKey('facial_employees.employee_id'))
    client_id = db.Column(db.Integer, db.ForeignKey('ast_client.client_id'))
    factory_id = db.Column(db.Integer, db.ForeignKey('ast_factory.factory_id'))
    detection_datetime = db.Column(db.DateTime, default=datetime.datetime.utcnow)
    person_detected = db.Column(db.Text, nullable=False)
    sop_followed = db.Column(db.Boolean, default=False, nullable=False)
    face_image_url = db.Column(db.Text, nullable=False)

    @classmethod
    def get_local_db_id(cls,client_id,factory_id):
        latest_row= cls.query.filter_by(client_id=client_id, factory_id=factory_id).order_by(desc(cls.local_db_log_id)).first()
        return latest_row.local_db_log_id if latest_row else None 
        
    @classmethod
    def get_by_log_id(cls, log_id):
        return cls.query.filter_by(log_id=log_id).first()
    
    @classmethod
    def get_records_by_employee_id(cls, employee_id):
        return cls.query.filter_by(employee_id=employee_id).all()
        
    @classmethod
    def get_records_by_client_factory_id(cls, client_id, factory_id):
        return cls.query.filter_by(client_id=client_id, factory_id=factory_id).all()
        
    @classmethod
    def get_all_records(cls):
        return cls.query.all()
        
    @classmethod
    def create(cls, data):
        new_rec = cls(
            local_db_log_id=data["log_id"],
            client_id=data["client_id"],
            factory_id=data["factory_id"],
            camera_id=data["camera_id"],
            employee_id=data["employee_id"],
            person_detected=data["person_detected"],
            sop_followed=data["sop_followed"],
            face_image_url=data["face_image_url"],
            detection_datetime=data["detection_datetime"]
        )
        db.session.add(new_rec)
        db.session.commit()
        return new_rec
        
    @classmethod
    def add_multiple_rows(cls, data):
        rows = []
        for item in data:
            new_record = cls(
                local_db_log_id=item.get("log_id"),
                client_id=item.get("client_id"),
                factory_id=item.get("factory_id"),
                camera_id=item.get("camera_id"),
                employee_id=item.get("employee_id"),
                person_detected=item.get("person_detected"),
                sop_followed=item.get("sop_followed"),
                face_image_url=item.get("face_image_url"),
                detection_datetime=item.get("detection_datetime")
            )
            rows.append(new_record)

        if rows:
            db.session.add_all(rows)
            db.session.commit()
        return True