FROM python:3.11-slim-bookworm

MAINTAIN<PERSON> <PERSON><PERSON><PERSON> <ka<PERSON><EMAIL>>

ENV DEBIAN_FRONTEND=noninteractive
ENV PYTHONWARNINGS="ignore:Unverified HTTPS request"

RUN apt-get update

RUN apt-get install -y bash
RUN python3 -m pip install --upgrade pip==24.3.1

RUN mkdir -p /usr/local/disruptlabs/uam_backend/api
RUN mkdir -p /var/log/gunicorn/

COPY requirements.txt /usr/local/disruptlabs/uam_backend/
RUN pip3 install -r /usr/local/disruptlabs/uam_backend/requirements.txt

COPY api /usr/local/disruptlabs/uam_backend/api
COPY *.py /usr/local/disruptlabs/uam_backend/


# Clean up
RUN apt-get clean && \
    rm -rf /var/lib/apt/lists/*

#Port expose
EXPOSE 8000

CMD bash -c "cd /usr/local/disruptlabs/uam_backend; /usr/local/bin/python3 /usr/local/bin/gunicorn --workers 4 --bind 0.0.0.0:8000 --timeout 1200 wsgi:app"

#For debugging
#COPY loop.sh /usr/local/disruptlabs/uam_backend/
#RUN chmod +x /usr/local/disruptlabs/uam_backend/loop.sh
#CMD ["/usr/local/disruptlabs/uam_backend/loop.sh"]



