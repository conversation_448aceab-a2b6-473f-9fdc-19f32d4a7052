from flask import request
from flask_restx import Resource, abort
from api.services import token_required
from api.utils.utils import generate_jwt_token

from api.payloads.reportPayloads import *
from api import report_ns
from api.controllers import reportController
from api.models.reportModels import *

import traceback



@report_ns.route("/send_mail")
@report_ns.doc("Send Mail")
class SendMail(Resource):
    @report_ns.expect(send_mail_payload)
    @report_ns.response(200, "Email sent successfully.")
    @report_ns.response(403, "Email could not be sent.")
    def post(self):
        try:
            # Retrieve JSON data
            data = request.json
            receivers = data.get('receivers', [])
            cc_emails = data.get('cc_emails', [])
            subject = data.get('subject', '')
            body = data.get('body', '')

            # Call the mail function
            response = reportController.send_mail(
                receivers=receivers,
                cc_emails=cc_emails,
                subject=subject,
                body=body
            )

            if response:
                return {"message": "Email sent successfully."}, 200
            else:
                return {"message": "Email sending failed."}, 403

        except Exception as e:
            # Log detailed error
            print(f"Error: {str(e)}")
            traceback.print_exc()
            return {"message": "Email could not be sent.", "error": str(e)}, 403


@report_ns.route("/get_user_tickets")
@report_ns.doc("Get Tickets by User, Client, Factory")
class GetTickets(Resource):
    @report_ns.expect(get_tickets_payload)
    @report_ns.response(200, "Tickets fetched successfully.")
    @report_ns.response(400, "Validation Error")
    def put(self):
        try:
            data = report_ns.payload
            req_fields = ["user_id", "client_id", "factory_id"]
            for field in req_fields:
                if not data.get(field):
                    return {"message": f"{field} is missing."}, 400
            
            tickets = Tickets.get_by_user_client_factory_id(data["user_id"], data["client_id"], data["factory_id"])
            return {"message": "Tickets fetched successfully.", "data": tickets}, 200
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})



@report_ns.route("/get_all_tickets")
@report_ns.doc("Get All Tickets")
class GetTickets(Resource):
    @report_ns.response(200, "Tickets fetched successfully.")
    @report_ns.response(400, "Validation Error")
    def get(self):
        try:
            tickets = Tickets.get_all_records()
            return {"message": "Tickets fetched successfully.", "data": tickets}, 200
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})


@report_ns.route("/get_ticket/<int:id>")
@report_ns.doc("Get Ticket")
class GetTicket(Resource):
    @report_ns.response(200, "Ticket fetched successfully.")
    @report_ns.response(400, "Validation Error")
    def get(self, id):
        try:
            ticket = Tickets.get_by_id(id)
            if ticket:
                return {"message": "Ticket fetched successfully.", "data": ticket}, 200
            else:
                return {"message": "Ticket not found."}, 404
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})

@report_ns.route("/get_tickets_by_client_factory")
@report_ns.doc("Get Tickets by Client, Factory")
class GetTickets(Resource):
    @report_ns.expect(get_tickets_by_client_factory_payload)
    @report_ns.response(200, "Tickets fetched successfully.")
    @report_ns.response(400, "Validation Error")
    def put(self):
        try:
            data = report_ns.payload
            req_fields = ["client_id", "factory_id"]
            for field in req_fields:
                if not data.get(field):
                    return {"message": f"{field} is missing."}, 400
            
            tickets = Tickets.get_by_client_factory_id(data["client_id"], data["factory_id"])
            return {"message": "Tickets fetched successfully.", "data": tickets}, 200
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})

@report_ns.route("/create_update_ticket")
@report_ns.doc("Create Ticket")
class CreateTicket(Resource):
    @report_ns.expect(create_uodate_ticket_payload)
    @report_ns.response(200, "Ticket created successfully.")
    @report_ns.response(400, "Validation Error")
    @report_ns.response(403, "Ticket creation failed.")
    def post(self):
        try:
            data = report_ns.payload
            req_fields = ["generated_by", "client_id", "factory_id", "priority", "query"]
            for field in req_fields:
                if not data.get(field):
                    return {"message": f"{field} is missing."}, 400
            
            ticket, message = Tickets.create_update(data)
            if ticket:
                return {"message": f"Ticket {message} successfully."}, 200
            else:
                return {"message": "Ticket creation failed."}, 403
            
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})
            

@report_ns.route("/toggle_ticket_status")
@report_ns.doc("Toggle Ticket Status")
class ToggleTicketStatus(Resource):
    @report_ns.expect(toggle_ticket_status_payload)
    @report_ns.response(200, "Ticket status toggled successfully.")
    @report_ns.response(400, "Validation Error")
    @report_ns.response(403, "Ticket status toggle failed.")
    def put(self):
        try:
            data = report_ns.payload
            req_fields = ["ticket_id", "status"]
            for field in req_fields:
                if not data.get(field):
                    return {"message": f"{field} is missing."}, 400
            
            ticket = Tickets.toggle_status(data)
            if ticket:
                return {"message": "Ticket status toggled successfully."}, 200
            else:
                return {"message": "Ticket status toggle failed."}, 403
            
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})
