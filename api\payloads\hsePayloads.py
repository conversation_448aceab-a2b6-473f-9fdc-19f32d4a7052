from api import hse_ns

from flask_restx import fields


# Add Module Payload
add_module_payload = hse_ns.model(
    "AddOrUpdateModule",
    {
        "module_id": fields.Integer(
            required=False, description="Module ID (for update only)"
        ),
        "name": fields.String(
            default="Helmet", required=True, description="The AI Module Name"
        ),
    },
)

insert_alerts_payload = hse_ns.model(
    "Insert Alerts",
    {
        "alerts": fields.List(
            fields.Nested(
                hse_ns.model(
                    "Alert",
                    {
                        "alert_id": fields.Integer(
                            required=True,
                            description="ID of the alert",
                        ),
                        "factory_id": fields.Integer(
                            required=True, description="Factory ID"
                        ),
                        "client_id": fields.Integer(
                            required=True, description="Client ID"
                        ),
                        "module_id": fields.Integer(
                            required=True, description="Module ID"
                        ),
                        "compliant": fields.Boolean(
                            required=True, description="Compliant"
                        ),
                        "unannotated_url": fields.String(
                            required=True, description="Unannotated URL"
                        ),
                        "annotated_url": fields.String(
                            required=True, description="Annotated URL"
                        ),
                        "camera_id": fields.String(
                            required=True, description="Camera ID"
                        ),
                    },
                )
            )
        )
    },
)

