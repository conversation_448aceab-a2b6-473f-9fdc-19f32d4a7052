from api import db
import datetime
from werkzeug.security import generate_password_hash
from sqlalchemy.orm import relationship
from sqlalchemy import <PERSON><PERSON><PERSON>

import json
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm.attributes import flag_modified
from sqlalchemy.orm import joinedload
from datetime import timedelta

class Users(db.Model):
    __tablename__ = "uam_user"  # Setting a custom table name
    user_id = db.Column(db.Integer, primary_key=True)
    client_id = db.Column(db.Integer, db.ForeignKey('ast_client.client_id'))
    factory_id = db.Column(db.Integer, db.<PERSON><PERSON>ey('ast_factory.factory_id'))
    name = db.Column(db.Text, nullable=False)
    email = db.Column(db.Text, nullable=False, unique=True)
    mobile_no = db.Column(db.Text, nullable=True)
    password = db.Column(db.Text, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    active = db.Column(db.Boolean, default=True, nullable=False)
    
    
    #Relationships
    roles = relationship('UserRole', backref='user',uselist=False)
    factories = relationship("UserFactory", backref="user", lazy='select')
    areas = relationship("UserArea", backref="user", lazy='select')

    email_notifications = relationship("EmailNotification", backref="user")
    whatsapp_notifications = relationship("WhatsAppNotification", backref="user")

    
   
    @classmethod
    def get_userid_by_name_and_area(cls, name, area):
        user = (
            cls.query
            .join(UserArea, UserArea.user_id == cls.user_id)
            .join(Area, Area.area_id == UserArea.area_id)
            .filter(
                cls.name == name,
                Area.name == area,  # Filtering by area name
                cls.active == True
            )
            .with_entities(cls.user_id)
            .first()
        )
        return user

    @classmethod
    def getAllusers(cls):
        users = cls.query.filter_by(active=True).all()
        return users

    @classmethod
    def get_all_userid(cls):
        users = cls.query.with_entities(cls.user_id, cls.active).distinct().all()
        return users

    @classmethod
    def get_all_super_admin(cls):
        users = cls.query.filter_by(active=True).all()
        return users

    @classmethod
    def get_name_by_user_id(cls, user_id):
        user = cls.query.with_entities(cls.name).filter_by(user_id=user_id).first()
        return user.name if user else ""

    @classmethod
    def get_user(cls, user_id):
        return cls.query.filter_by(user_id=user_id).first()

    @classmethod
    def get_by_user_id(cls, user_id):
        return cls.query.filter_by(user_id=user_id, active=True).first()

    @classmethod
    def get_by_email(cls, email):
        return cls.query.filter_by(email=email, active=True).first()

    @classmethod
    def get_by_email_other(cls, email, user_id):
        return cls.query.filter(
            cls.user_id != user_id, cls.email == email, cls.active == True
        ).first()

    @classmethod
    def get_user_role(cls, user_id):
        user = cls.query.get(user_id)
        if user:
            return user.roles.role.role_name
        return None

    @classmethod
    def create_user(cls, data):
        hashed_password = generate_password_hash(data["password"], method="pbkdf2")
        new_user = Users(
            client_id=data["client_id"],
            factory_id=data["factory_id"],
            name=data["name"].strip(),
            email=data["email"].strip(),
            mobile_no=data["mobile_no"].strip(),
            password=hashed_password,
            active=True,
        )
        db.session.add(new_user)
        db.session.flush()
        # db.session.commit()
        return new_user

    @classmethod
    def create_multiple_users(cls,data):
        result=[]
        for user in data["users"]:
           hashed_password = generate_password_hash(user["password"], method="pbkdf2")
           new_user=cls(
                client_id=data["client_id"],
                factory_id=data["factory_id"],
                email=user["email"],
                name=user["name"],
                password=hashed_password,
                active=True, 
                created_at=datetime.datetime.now()
           )
           result.append(new_user)
           
        db.session.add_all(result)
        db.session.commit()
        return [user.user_id for user in result]
    
    @classmethod
    def loginUser(cls, data):
        user = cls.query.filter_by(email=data["email"]).first()
        if user:
            return user
        return None
    
    def update(self, data):
        if data["email"]:
            self.email = data["email"].strip()
        if data["name"]:
            self.name = data["name"].strip()
        if data["mobile_no"]:
            self.mobile_no = data["mobile_no"].strip()
        self.updated_at = datetime.datetime.utcnow()
        db.session.commit()
        return self

    @classmethod
    def delete_user(cls, user_id):
        user_info = cls.get_by_user_id(user_id)
        if user_info:
            # Delete User Factories , User Areas, AI Models
            for user_factory in user_info.factories:
                for user_area in user_info.areas:
                    for sub_area in user_area.area.sub_areas:
                        for camera in sub_area.cameras:
                            if camera.image: 
                                db.session.delete(camera.image)
      
                            db.session.delete(camera)

                        db.session.delete(sub_area)
   
                    for target in user_area.area.area_targets:
                        db.session.delete(target)

                    db.session.delete(user_area)

                    
                for shifts in user_factory.factory.factory_shifts: 
                        db.session.delete(shifts)                
                db.session.delete(user_factory)


            for model in user_info.AI_models:
                db.session.delete(model)

            for whatsapp_notif in user_info.whatsapp_notifications:
                db.session.delete(whatsapp_notif)

            for email_notif in user_info.email_notifications:
                db.session.delete(email_notif)


            if user_info.roles:
                db.session.delete(user_info.roles)
            db.session.delete(user_info)
            db.session.commit()
            return True
        else:
            return False

    @classmethod
    def toggle_user_active(cls, user_id):
        user = cls.query.get(user_id)
        if user:
            user.active = not user.active
            user.updated_at = datetime.datetime.utcnow()
            db.session.commit()
            return user
        return None

# Role Model
class Role(db.Model):
    __tablename__ = 'uam_role'
    
    role_id = db.Column(db.Integer, primary_key=True)
    role_name = db.Column(db.Text, unique=True, nullable=False)
    description = db.Column(db.Text, nullable=True)
    
    user_roles = relationship('UserRole', backref='role')
    role_permissions = relationship('RolePermission', backref='role')

    @classmethod
    def create(cls, role_name, description=None):
        new_role = cls(role_name=role_name, description=description)
        db.session.add(new_role)
        db.session.commit()
        return new_role

    @classmethod
    def get_by_id(cls, role_id):
        return cls.query.get(role_id)    
    

    @classmethod
    def get_by_name(cls, role_name):
        return cls.query.filter_by(role_name=role_name).first()

    @classmethod
    def get_all(cls):
        return cls.query.all()

    def update(self, role_name=None, description=None):
        if role_name:
            self.role_name = role_name
        if description is not None:
            self.description = description
        db.session.commit()
        return self

    def delete(self):    
        for i in self.role_permissions:
                print(i)
                db.session.delete(i)
        for i in self.user_roles:
                print(i)
                db.session.delete(i)        
        db.session.delete(self)
        db.session.commit()

# Permission Model
class Permission(db.Model):
    __tablename__ = 'uam_permission'
    
    permission_id = db.Column(db.Integer, primary_key=True)
    permission_name = db.Column(db.Text, unique=True, nullable=False)
    description = db.Column(db.Text, nullable=True)
    
    role_permissions = relationship('RolePermission', backref='permission')

    @classmethod
    def create(cls, permission_name, description=None):
        new_permission = cls(permission_name=permission_name, description=description)
        db.session.add(new_permission)
        db.session.commit()
        return new_permission

    @classmethod
    def get_by_id(cls, permission_id):
        return cls.query.get(permission_id)

    @classmethod
    def get_by_name(cls, permission_name):
        return cls.query.filter_by(permission_name=permission_name).first()

    @classmethod
    def get_all(cls):
        return cls.query.all()

    def update(self, permission_name=None, description=None):
        if permission_name:
            self.permission_name = permission_name
        if description is not None:
            self.description = description
        db.session.commit()

    def delete(self):
        db.session.delete(self)
        db.session.commit()

# UserRole Model
class UserRole(db.Model):
    __tablename__ = 'uam_user_role'
    
    user_role_id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    user_id = db.Column(db.Integer, ForeignKey('uam_user.user_id'), nullable=False)
    role_id = db.Column(db.Integer, ForeignKey('uam_role.role_id'), nullable=False)

    @classmethod
    def create(cls, user_id, role_id):
        new_user_role = cls(user_id=user_id, role_id=role_id)
        db.session.add(new_user_role)
        # db.session.commit()
        return new_user_role
    
    @classmethod
    def create_multiple(cls, user_ids, role_id):
        result=[]
        for user in user_ids:
            new_user_role = cls(user_id=user, role_id=role_id)
            result.append(new_user_role)
            
        db.session.add_all(result)
        db.session.commit()
        return True

    @classmethod
    def get_by_id(cls, user_role_id):
        return cls.query.get(user_role_id)

    @classmethod
    def get_by_user_and_role(cls, user_id, role_id):
        return cls.query.filter_by(user_id=user_id, role_id=role_id).first()
    
    @classmethod
    def get_by_user_id(cls, user_id):
            return cls.query.filter_by(user_id=user_id).first()

    @classmethod
    def get_all(cls):
        return cls.query.all()

    def update(self, user_id=None, role_id=None):
        if user_id is not None:
            self.user_id = user_id
        if role_id is not None:
            self.role_id = role_id
        db.session.commit()
        return self

    def delete(self):
        db.session.delete(self)
        db.session.commit()

# RolePermission Model
class RolePermission(db.Model):
    __tablename__ = 'uam_role_permission'
    
    role_permission_id = db.Column(db.Integer, primary_key=True)
    role_id = db.Column(db.Integer, ForeignKey('uam_role.role_id'), nullable=False)
    permission_id = db.Column(db.Integer, ForeignKey('uam_permission.permission_id'), nullable=False)

    @classmethod
    def create(cls, role_id, permission_id):
        new_role_permission = cls(role_id=role_id, permission_id=permission_id)
        db.session.add(new_role_permission)
        db.session.commit()
        return new_role_permission

    @classmethod
    def get_by_id(cls, role_permission_id):
        return cls.query.get(role_permission_id)


    @classmethod
    def get_by_role_id(cls, role_id):
        return cls.query.filter_by(role_id=role_id).all()
    
    @classmethod
    def get_by_role_and_permission(cls, role_id, permission_id):
        return cls.query.filter_by(role_id=role_id, permission_id=permission_id).first()

    @classmethod
    def get_all(cls):
        return cls.query.all()

    def update(self, role_id=None, permission_id=None):
        if role_id is not None:
            self.role_id = role_id
        if permission_id is not None:
            self.permission_id = permission_id
        db.session.commit()

    def delete(self):
        db.session.delete(self)
        db.session.commit()

class UserLog(db.Model):
    __tablename__ = "uam_user_logs"
    log_id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    user_id = db.Column(db.Integer, ForeignKey('uam_user.user_id'), nullable=False, unique=True)
    login_time = db.Column(db.DateTime, nullable=True)
    logout_time = db.Column(db.DateTime, nullable=True)
    duration_time = db.Column(db.Integer, nullable=True)
    accumulated_duration = db.Column(db.Integer, nullable=True)
    

    @classmethod
    def get_all_records(cls):
        return cls.query.all()

    @classmethod
    def get_accumulatedduration_by_userID(cls, user_id):
        return cls.query.with_entities(cls.accumulated_duration).filter_by(user_id=user_id).first()
    
    @classmethod
    def get_all_accumulated_durations(cls):
        return cls.query.with_entities(cls.accumulated_duration).all()

    @classmethod
    def create(cls, data):
        try:
            existed_user_log = cls.query.filter_by(user_id=data['user_id']).first()
            if not existed_user_log:
                newRecord = cls(
                    user_id=data['user_id'],
                    login_time=data['login_time'],
                )
                db.session.add(newRecord)
                db.session.commit()
                return newRecord
            else:
                existed_user_log.login_time = data['login_time']
                existed_user_log.logout_time = None
                existed_user_log.duration_time = None
                db.session.commit()
        except Exception as e:
            db.session.rollback()
            raise Exception(f"Error creating user log: {e}")
        
    @classmethod
    def set_logout_time(cls, userID):
        latest_log = cls.query.filter_by(user_id=userID).order_by(cls.login_time.desc()).first()
        if latest_log:
            latest_log.logout_time = datetime.datetime.utcnow()
            db.session.commit()
            return latest_log
        else:
            raise Exception("No log found for this user.")
        
    @classmethod
    def set_duration_time(cls, userID):
        try:
            # Get the latest log entry for the user
            latest_log = cls.query.filter_by(user_id=userID).order_by(cls.login_time.desc()).first()
            if latest_log and latest_log.logout_time and latest_log.login_time:
                # Calculate the duration in seconds (datetime.timedelta handles multi-day spans)
                delta = latest_log.logout_time - latest_log.login_time
                total_seconds = int(delta.total_seconds())
                
                # Set the calculated duration time
                latest_log.duration_time = total_seconds
                
                # Commit the updated duration to the database
                db.session.commit()
                return latest_log
            else:
                raise Exception("Invalid log entry: Either no log found, or login/logout times are incomplete.")
        except Exception as e:
            db.session.rollback()
            raise Exception(f"Error setting duration time: {e}")

        
    @classmethod
    def reset_accumulated_duration(cls):
        try:
            with db.session.begin():
                db.session.query(cls).update({cls.accumulated_duration: None})
                db.session.commit()
        except Exception as e:
            db.session.rollback()
            raise Exception(f"Error resetting accumulated durations: {e}")
        
    @classmethod
    def set_accumulated_duration(cls, userID):
        latest_log = cls.query.filter_by(user_id=userID).order_by(cls.login_time.desc()).first()
        if not latest_log.accumulated_duration:
            latest_log.accumulated_duration = latest_log.duration_time
        else:
            latest_log.accumulated_duration += latest_log.duration_time
        db.session.commit()


class UserFactory(db.Model):
    __tablename__ = 'uam_user_factory'

    user_factory_id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('uam_user.user_id'), nullable=False)
    factory_id = db.Column(db.Integer, db.ForeignKey('ast_factory.factory_id'), nullable=False)

    @classmethod
    def is_user_associated_with_factory(cls,user_id, factory_id):
        return db.session.query(cls).filter_by(cls.user_id==user_id, cls.factory_id==factory_id).first() is not None


    @classmethod
    def create(cls, user_id, factory_id):
        new_user_factory = cls(user_id=user_id, factory_id=factory_id)
        db.session.add(new_user_factory)
        # db.session.commit()
        return new_user_factory
    
    @classmethod
    def update_user_factory(cls, user_id, factory_id):
        get_record=cls.query.filter_by(cls.user_id==user_id).first()
        if get_record:
            get_record.factory_id= factory_id
            db.session.commit()
        else:
            get_record = cls(user_id=user_id, factory_id=factory_id)
            db.session.add(get_record)  
            db.session.commit()
            
        return get_record
    
    @classmethod
    def create_multiple(cls, user_id, factory_ids):
        new_user_factories = []
        for factory_id in factory_ids:
            new_user_factory = cls(user_id=user_id, factory_id=factory_id)
            db.session.add(new_user_factory)
            new_user_factories.append(new_user_factory)
        db.session.commit()
        return new_user_factories

    @classmethod
    def get_by_id(cls, user_factory_id):
        return cls.query.get(user_factory_id)

    @classmethod 
    def get_by_user_id(cls, user_id):
        return cls.query.filter_by(user_id=user_id).all()
    
    @classmethod
    def get_by_factory_id(cls, factory_id):
        return cls.query.filter_by(factory_id=factory_id).all()
    
    @classmethod
    def get_by_user_and_factory(cls, user_id, factory_id):
        return cls.query.filter_by(user_id=user_id, factory_id=factory_id).first()

    @classmethod
    def get_all(cls):
        return cls.query.all()

    def update(self, user_id=None, factory_id=None):
        if user_id is not None:
            self.user_id = user_id
        if factory_id is not None:
            self.factory_id = factory_id
        db.session.commit()
        return self

    def delete(self):
        db.session.delete(self)
        db.session.commit()
            
    @classmethod
    def update_user_factories(cls, user_id, factory_ids_list):
        # Get current factories for this user
        current_factories = db.session.query(cls.factory_id).filter_by(user_id=user_id).all()
        current_factory_ids = {factory.factory_id for factory in current_factories}

        # Factories to add (in the list but not associated with the user)
        factories_to_add = set(factory_ids_list) - current_factory_ids

        # Factories to remove (associated with the user but not in the list)
        factories_to_remove = current_factory_ids - set(factory_ids_list)

        # Add new factories
        for factory_id in factories_to_add:
            new_user_factory = cls(user_id=user_id, factory_id=factory_id)
            db.session.add(new_user_factory)

        # Remove extra factories
        for factory_id in factories_to_remove:
            db.session.query(cls).filter_by(user_id=user_id, factory_id=factory_id).delete()

        db.session.commit()
        
        
class UserArea(db.Model):
    __tablename__ = 'uam_user_area'

    user_area_id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('uam_user.user_id'), nullable=False)
    area_id = db.Column(db.Integer, db.ForeignKey('ast_area.area_id'), nullable=False)

    @classmethod
    def create(cls, user_id, area_id):
        new_user_area = cls(user_id=user_id, area_id=area_id)
        db.session.add(new_user_area)
        db.session.commit()
        return new_user_area
    
    @classmethod
    def create_multiple(cls, user_id, areas_ids):
        new_user_areas = []
        for area_id in areas_ids:
            new_user_area = cls(user_id=user_id, area_id=area_id)
            db.session.add(new_user_area)
            new_user_areas.append(new_user_area)
        db.session.commit()
        return new_user_areas
    
    @classmethod
    def get_dict_of_area_ids_by_name_list(cls, area_names, factory_id):
        result = cls.query.with_entities(cls.name, cls.area_id).filter(
            cls.name.in_(area_names),
            cls.factory_id == factory_id
        ).all()
        return {name: area_id for name, area_id in result}

    @classmethod
    def get_by_id(cls, user_area_id):
        return cls.query.get(user_area_id)

    @classmethod 
    def get_by_user_id(cls, user_id):
        return cls.query.filter_by(user_id=user_id).all()
    
    @classmethod
    def get_by_area_id(cls, area_id):
        return cls.query.filter_by(area_id=area_id).all()
    
    @classmethod
    def get_by_user_and_area(cls, user_id, area_id):
        return cls.query.filter_by(user_id=user_id, area_id=area_id).first()
    
    

    @classmethod
    def get_all(cls):
        return cls.query.all()

    def update(self, user_id=None, area_id=None):
        if user_id is not None:
            self.user_id = user_id
        if area_id is not None:
            self.area_id = area_id
        db.session.commit()
        return self

    def delete(self):
        db.session.delete(self)
        db.session.commit()
        
    @classmethod
    def update_user_areas(cls, user_id, area_ids_list):
        # Get current areas for this user
        current_areas = db.session.query(cls.area_id).filter_by(user_id=user_id).all()
        current_area_ids = {area.area_id for area in current_areas}

        # Areas to add (in the list but not associated with the user)
        areas_to_add = set(area_ids_list) - current_area_ids

        # Areas to remove (associated with the user but not in the list)
        areas_to_remove = current_area_ids - set(area_ids_list)

        # Add new areas
        for area_id in areas_to_add:
            new_user_area = cls(user_id=user_id, area_id=area_id)
            db.session.add(new_user_area)

        # Remove extra areas
        for area_id in areas_to_remove:
            deleted_count = db.session.query(cls).filter_by(user_id=user_id, area_id=area_id).delete()

        # Commit the transaction
        db.session.commit()
            