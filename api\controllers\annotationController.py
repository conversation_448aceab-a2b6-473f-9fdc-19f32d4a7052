import os
import calendar
import requests
import yaml
import time

from collections import Counter
from PIL import Image as pil_Image
from collections import defaultdict

from api.utils.utils import *
from api.utils.gcp_storage import *
from api import db

from api.models.uamModels import *
from api.models.annotationModels import *
from api.models.assetModels import *
from api.models.hseModels import *

from sqlalchemy import and_, or_, func, case, extract
from sqlalchemy.dialects.postgresql import ARRAY

from datetime import datetime, timedelta
from collections import defaultdict
from sqlalchemy.sql import text


def get_unannotated_images(data):
    try:
        user_id = data["user_id"]
        client_id = data["client_id"]
        factory_id = data["factory_id"]
        batch_id = data["batch_id"]
        limit = data["limit"]
        job_id = data["job_id"]

        unannotated_images = (
            db.session.query(AnnotationCameraImages)
            .filter(
                and_(
                    AnnotationCameraImages.annotation_status == False,
                    AnnotationCameraImages.disqualified == False,
                    AnnotationCameraImages.background_image == False,
                    AnnotationCameraImages.client_id == client_id,
                    AnnotationCameraImages.factory_id == factory_id,
                    AnnotationCameraImages.batch_id == batch_id,
                    AnnotationCameraImages.job_id == job_id,
                    AnnotationCameraImages.annotation_user_id == user_id
                )
            )
            .limit(5)
            .all()
        )

        results = []
        if unannotated_images:
            for record in unannotated_images:
                results.append({
                    "id": record.id,
                    "camera_id": record.camera_id,
                    "image_url": record.image_url,
                    "annotation_user_id": record.annotation_user_id,
                    "annotation": record.annotation,
                    "rejected": record.rejected,
                    "review_status": record.review_status,
                    "review_comments": record.review_comments,
                    "detection_model": record.detection_model,
                    "modules": Modules.get_records_by_list(
                        AnnotationYoloClasses.get_modules(record.client_id, record.factory_id, record.batch_id)
                    ),
                    "job_id": record.job_id,
                    "created_at": record.created_at.strftime("%Y-%m-%d"),
                    "updated_at": record.updated_at.strftime("%Y-%m-%d")
                })

        return results

    except Exception as e:
        print(f"Error in get_unannotated_images: {e}")
        return {"error": str(e)}
    
    
def get_review_images(data):
    
    try:
        
        user_id = data["user_id"]
        annotation_user_id = data["annotation_user_id"]
        client_id = data["client_id"]
        factory_id = data["factory_id"]
        batch_id = data["batch_id"]
        limit = data["limit"]
        job_id = data["job_id"]
        
        
        review_images = (
            db.session.query(AnnotationCameraImages)
            .filter(
                and_(
                    AnnotationCameraImages.annotation_status == True,
                    AnnotationCameraImages.review_status == False,
                    AnnotationCameraImages.background_image == False,
                    AnnotationCameraImages.client_id == client_id,
                    AnnotationCameraImages.factory_id == factory_id,
                    AnnotationCameraImages.batch_id == batch_id,
                    AnnotationCameraImages.annotation_user_id == annotation_user_id,
                    AnnotationCameraImages.job_id == job_id,
                    AnnotationCameraImages.dataset_id == 0,
                    or_(
                        AnnotationCameraImages.review_user_id == 0,
                        AnnotationCameraImages.review_user_id == user_id
                    )
                )
            )
            .limit(5)
            .all()
        )

        results = []
        if len(review_images) > 0:
        
            # Update review_user_id and commit
            # And also make the result
            
            for record in review_images:
                record.review_user_id = user_id
                
                review_result =     {
                    "id": record.id,
                    "camera_id": record.camera_id,
                    "image_url": record.image_url,
                    "annotation_user_id": record.annotation_user_id,
                    "annotation_user_name": Users.get_name_by_user_id(record.annotation_user_id),
                    "annotation": record.annotation,
                    "disqualified": record.disqualified,
                    "detection_model": record.detection_model,
                    "background_image": record.background_image,
                    "modules": Modules.get_records_by_list(AnnotationYoloClasses.get_modules(record.client_id, record.factory_id, record.batch_id)),
                    "job_id": record.job_id,
                    "created_at": record.created_at.strftime("%Y-%m-%d"),
                    "updated_at": record.updated_at.strftime("%Y-%m-%d")
                }
                
                results.append(review_result)

            db.session.commit()
            
        return results
        
    except Exception as e:
        print(f"Error in get_review_images: {e}")
        return {"error": str(e)}


def get_annotation_summary(data):
    
    try:
        user_id = data["user_id"]
        
        records = db.session.query(
            AnnotationCameraImages.client_id,
            AnnotationCameraImages.factory_id,
            AnnotationCameraImages.batch_id,
            func.count(AnnotationCameraImages.id).filter(AnnotationCameraImages.rejected == True).label('rejected_images_count'),
            func.count(AnnotationCameraImages.id).filter(AnnotationCameraImages.disqualified == True).label('disqualified_images_count'),
            func.count(AnnotationCameraImages.id).filter(AnnotationCameraImages.annotation_status == True).label('annotated_images_count'),
            func.count(AnnotationCameraImages.id).filter(AnnotationCameraImages.annotation_status == False,AnnotationCameraImages.disqualified == False).label('unannotated_images_count'),
            func.count(AnnotationCameraImages.id).filter(AnnotationCameraImages.background_image == True).label('background_images_count'),
        ).select_from(AnnotationCameraImages
        ).filter( 
            AnnotationCameraImages.dataset_id == 0
        ).group_by(
            AnnotationCameraImages.client_id,
            AnnotationCameraImages.factory_id,
            AnnotationCameraImages.batch_id
        ).all() 

        results = []
        
        existing_client_factory_keys = set()
        existing_client_factory_batch_keys = set()

        for r in records:
            key = (r.client_id, r.factory_id)
            existing_client_factory_keys.add(key)

            key2 = (r.client_id, r.factory_id, r.batch_id)
            existing_client_factory_batch_keys.add(key2)

            client_name = Client.get_name_by_id(r.client_id)
            factory_name = Factory.get_name_by_id(r.factory_id)

            result = {
                "client_id": r.client_id,
                "client_name": client_name,
                "factory_id": r.factory_id,
                "factory_name": factory_name,
                "batch_id": r.batch_id,
                "annotated_images_count": r.annotated_images_count,
                "unannotated_images_count": r.unannotated_images_count,
                "rejected_images_count": r.rejected_images_count,
                "disqualified_images_count": r.disqualified_images_count,
                "background_images_count": r.background_images_count,
                "Modules": Modules.get_records_by_list(AnnotationYoloClasses.get_modules(r.client_id, r.factory_id, r.batch_id))
            }
            results.append(result)
           
        yolo_records = AnnotationYoloClasses.get_all_records()
        
        for yolo in yolo_records:
            key2 = (yolo.client_id, yolo.factory_id, yolo.batch_id)
            if key2 not in existing_client_factory_batch_keys:
                existing_client_factory_batch_keys.add(key2)  # avoid duplicates

                client_name = Client.get_name_by_id(yolo.client_id)
                factory_name = Factory.get_name_by_id(yolo.factory_id)

                result = {
                    "client_id": yolo.client_id,
                    "client_name": client_name,
                    "factory_id": yolo.factory_id,
                    "factory_name": factory_name,
                    "batch_id": yolo.batch_id,
                    "annotated_images_count": 0,
                    "unannotated_images_count": 0,
                    "rejected_images_count": 0,
                    "disqualified_images_count": 0,
                    "background_images_count": 0,
                    "Modules": Modules.get_records_by_list(AnnotationYoloClasses.get_modules(yolo.client_id, yolo.factory_id, yolo.batch_id))
                }
                results.append(result)

        all_factories = Factory.get_all_records()

        for factory in all_factories:
            key = (factory.client_id, factory.factory_id)
            if key not in existing_client_factory_keys:
                client_name = Client.get_name_by_id(factory.client_id)
                factory_name = Factory.get_name_by_id(factory.factory_id)

                result = {
                    "client_id": factory.client_id,
                    "client_name": client_name,
                    "factory_id": factory.factory_id,
                    "factory_name": factory_name,
                    "batch_id": 1,
                    "annotated_images_count": 0,
                    "unannotated_images_count": 0,
                    "rejected_images_count": 0,
                    "disqualified_images_count": 0,
                    "background_images_count": 0,
                    "Modules": Modules.get_records_by_list(AnnotationYoloClasses.get_modules(factory.client_id, factory.factory_id, 1))
                }
                results.append(result)

        return results
    
    except Exception as e:
        print(f"Error in get_annotation_summary: {e}")
        return {"error": str(e)}
    
    
def get_dataset_summary(data):
    
    try:
        user_id = data["user_id"]

        datasets = AnnotationDataset.get_all_records()

        results = [
        {
            "dataset_id": record.dataset_id,
            "batch_id": record.batch_id,
            "name": record.name,
            "images": record.images,
            "dataset_url": record.dataset_url,
            "dataset_size": record.dataset_size,
            "client_id":record.client_id,
            "client_name": Client.get_name_by_id(record.client_id),
            "factory_id":record.factory_id,
            "factory_name": Factory.get_name_by_id(record.factory_id),
            "description": record.description,
            "created_at": record.created_at.strftime("%Y-%m-%d"),
            "updated_at": record.updated_at.strftime("%Y-%m-%d")
        } for record in datasets
        ] 
        
        return results

    except Exception as e:
        print(f"Error in get_dataset_summary: {e}")
        return {"error": str(e)}
        
        
def fetch_image_with_retry(url, retries=2, timeout=4, delay=1):
    for attempt in range(retries):
        try:
            response = requests.get(url, timeout=timeout)
            if response.status_code == 200:
                return response
            elif response.status_code == 404:
                print(f"[Abort] Image URL not found (404): {url}")
                return None
            else:
                print(f"[Retry {attempt+1}/{retries}] HTTP {response.status_code} error from: {url}")
        except (requests.exceptions.Timeout, requests.exceptions.ConnectionError) as e:
            print(f"[Retry {attempt+1}/{retries}] Connection error: {e}")
        time.sleep(delay)
    print(f"[Error] Failed to fetch image after {retries} attempts: {url}")
    return None
    
    
def generate_dataset(data):
    try:
        success = True
        
        client_id = data["client_id"]
        client_name = Client.get_name_by_id(client_id) if Client.get_name_by_id(client_id) else None
        if client_name is None:
            success = False
            message = f"Client ID: {client_id} not exist in database."
            return success, message
        factory_id = data["factory_id"]
        factory_name = Factory.get_name_by_id(factory_id) if Factory.get_name_by_id(factory_id) else None
        if factory_name is None:
            success = False
            message = f"Factory ID: {factory_id} not exist in database."
            return success, message
            
        batch_id = data["batch_id"]
        
        image_records = db.session.query(
            AnnotationCameraImages
        ).select_from(AnnotationCameraImages
        ).filter(
            AnnotationCameraImages.client_id == client_id,
            AnnotationCameraImages.factory_id == factory_id,
            AnnotationCameraImages.batch_id == batch_id,
        ).filter(
            AnnotationCameraImages.dataset_id == 0,
            AnnotationCameraImages.rejected == False,
            AnnotationCameraImages.disqualified == False,
            AnnotationCameraImages.annotation_status == True,
            AnnotationCameraImages.background_image == False
        ).limit(4000).all()
        
        print("Total image_records:", len(image_records))
        
        total_number_of_image_records = len(image_records)
        if total_number_of_image_records > 0:
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            dataset_folder = f"{client_name.lower()}_{factory_name.lower()}_{timestamp}"
            # Create the folder if it doesn't exist
            os.makedirs(dataset_folder, exist_ok=True)
            images_test_path = os.path.join(dataset_folder, 'test/images')
            images_train_path = os.path.join(dataset_folder, 'train/images')
            images_val_path = os.path.join(dataset_folder, 'val/images')
            labels_test_path = os.path.join(dataset_folder, 'test/labels')
            labels_train_path = os.path.join(dataset_folder, 'train/labels')
            labels_val_path = os.path.join(dataset_folder, 'val/labels')
            os.makedirs(images_test_path, exist_ok=True)
            os.makedirs(images_train_path, exist_ok=True)
            os.makedirs(images_val_path, exist_ok=True)
            os.makedirs(labels_test_path, exist_ok=True)
            os.makedirs(labels_train_path, exist_ok=True)
            os.makedirs(labels_val_path, exist_ok=True)
            
            train_image_count = int(round(0.85 * total_number_of_image_records))
            val_image_count = int(round(0.13 * total_number_of_image_records))
            
            #Create yolo index file
            yolo_record = AnnotationYoloClasses.get_record_by_client_factory_batch_id(client_id=client_id,factory_id=factory_id, batch_id=batch_id)
            if yolo_record is not None:
                names = Modules.get_records_by_list(yolo_record.modules)
                data = {
                    'names': names,
                    'nc': len(names),
                    'test': 'test/images',
                    'train': 'train/images',
                    'val': 'val/images'
                }
                yolo_index_file_path = os.path.join(dataset_folder, 'data.yml')
                with open(yolo_index_file_path, 'w') as file:
                    yaml.dump(data, file, default_flow_style=False)

            
            images_dest_folder = images_train_path
            labels_dest_folder = labels_train_path
            counter = 1
            resolution_counts = defaultdict(int)
            for record in image_records:
                
                #Get yolo data from database record
                data_str = record.annotation
                # print("annotation:", data_str)

                if data_str == "{}":
                    print("No annotation found")
                    continue
                
                items = data_str.strip('{}').split('","')
                yolo_data = [item.strip('"') for item in items]
                
                class_ids = [int(line.split()[0]) for line in yolo_data]
                invalid_ids = [i for i in class_ids if i < 0 or i >= len(names)]
                if invalid_ids:
                    print("Found invalid class IDs:", invalid_ids)
                    continue
                    
                counts = Counter(class_ids) #Count the objects for dataset summary file
                
                if counter > train_image_count and counter < (train_image_count + val_image_count):
                    images_dest_folder = images_val_path
                    labels_dest_folder = labels_val_path
                if counter > (train_image_count + val_image_count): #Remaining will be test
                    images_dest_folder = images_test_path
                    labels_dest_folder = labels_test_path 
               

                #extract image name from record.image_url
                image_name = f"{record.camera_id}.{record.id}"
                
                #download image
                #print("image_url:", record.image_url)
                
                response = fetch_image_with_retry(record.image_url)

                if response:
                    image_file_name = f"{image_name}.jpg"
                    image_file_path = os.path.join(images_dest_folder, image_file_name)
                    with open(image_file_path, "wb") as f:
                        f.write(response.content)

                    # Read resolution
                    with pil_Image.open(image_file_path) as img:
                        width, height = img.size
                        resolution_key = f"{width}x{height}"
                        resolution_counts[resolution_key] += 1
                else:
                    print(f"Skipping image: {record.image_url}")
                    continue
                    
            
                
                # yolo_data = [
                    # "0 0.159545 0.252839 0.159091 0.255000",
                    # "1 0.554091 0.693672 0.288182 0.306667"
                # ]
                
                #create yolo annotation file
                yolo_file_name = f"{image_name}.txt"
                
                
                
                file_path = os.path.join(labels_dest_folder, yolo_file_name)
                with open(file_path, "w") as f:
                    for line in yolo_data:
                        f.write(line + "\n")
                
               
                counter = counter + 1
            

            background_images = 0
            annotated_images = total_number_of_image_records
            total_images = background_images + annotated_images
            
            # Build resolution distribution dictionary
            image_size_distribution = {}
            for resolution, count in resolution_counts.items():
                image_size_distribution[resolution] = count 
                
                
            # Print counts for each class
            # for i, name in enumerate(names):
                # print(f"{name}: {counts.get(i, 0)}")

            object_counts = {
                f"{names[class_id]} ({class_id})": count
                for class_id, count in counts.items()
            }


            # Write dataset summary file
            summary_data = {
                'Total images': total_images,
                'Background images (empty TXT)': background_images,
                'Annotated images': annotated_images,
                'Object counts': object_counts,
                'Image size distribution': image_size_distribution
            }
            
            dataset_summary_file_path = os.path.join(dataset_folder, 'dataset_summary.txt')
            with open(dataset_summary_file_path, 'w') as summary_file:
                yaml.dump(summary_data, summary_file, default_flow_style=False)
                    
            
            #Compress the folder
            zip_file = f"{dataset_folder}.zip"
            zip_folder(folder_path=dataset_folder, output_path=zip_file)
            
            # Get the size of the zip file in bytes
            zip_file_size = get_file_size(file_path=zip_file)
            
            #Upload the file in GCP storage
            destination_blob_name = f"yolo_dataset/{zip_file}"
            upload_url = upload_blob(bucket_name="disrupt-hse-images",source_file_name=zip_file,destination_blob_name=destination_blob_name)
            
            #Delete folder and file
            delete_file(zip_file)
            delete_folder(dataset_folder)
            
            data = {
                "client_id": client_id,
                "factory_id": factory_id,
                "batch_id": batch_id,
                "name": zip_file,
                "images": total_number_of_image_records,
                "dataset_url": upload_url,
                "dataset_size": zip_file_size,
                "description": f"Yolo dataset"
            }
            
            dataset_record = AnnotationDataset.create(data)
            
            #Update AnnotationDataset ID in image records
            updated_data = [
                {
                    'id': record.id,
                    'dataset_id': dataset_record.dataset_id
                }
                for record in image_records
            ]
            AnnotationCameraImages.bulk_update(updated_data)
            
            success = True
            message = "Generating dataset! It will take time .."
        
        else:
            success = False
            message = f"Annotated images not available for the New Dataset."
                
        return success, message

    except Exception as e:
        print(f"Error in generate_dataset: {e}")
        return {"success": False, "message": str(e)}


def get_user_summary(data):
    try:
        user_id = data["user_id"]

        record = db.session.query(
            func.count(AnnotationCameraImages.id).filter(AnnotationCameraImages.rejected == True).label('rejected_images_count'),
            func.count(AnnotationCameraImages.id).filter(AnnotationCameraImages.disqualified == True).label('disqualified_images_count'),
            func.count(AnnotationCameraImages.id).filter(AnnotationCameraImages.annotation_status == True).label('annotated_images_count'),
            func.count(AnnotationCameraImages.id).filter(AnnotationCameraImages.annotation_status == False,AnnotationCameraImages.disqualified == False).label('unannotated_images_count')
        ).select_from(AnnotationCameraImages
        ).filter( 
            AnnotationCameraImages.annotation_user_id == user_id
        ).group_by(
            AnnotationCameraImages.annotation_user_id
        ).first() 
        
        user_summary = {
            "user_id": user_id,
            "rejected_images_count": record.rejected_images_count,
            "disqualified_images_count": record.disqualified_images_count,
            "annotated_images_count": record.annotated_images_count,
            "unannotated_images_count": record.unannotated_images_count
        }

        return user_summary
        
    except Exception as e:
        print(f"Error in get_user_summary: {e}")
        return {"error": str(e)}


def get_user_monthly_chart(data):
    try:
        user_id = data["user_id"]

        today = datetime.utcnow()
        year = today.year
        month = today.month
        days_in_month = calendar.monthrange(year, month)[1]

        # Subquery grouped by day
        daily_stats = db.session.query(
            extract('day', AnnotationCameraImages.updated_at).label('day'),
            func.count(case((AnnotationCameraImages.annotation_status == True, 1))).label('annotated_images_count'),
            #func.count(case((AnnotationCameraImages.rejected == True, 1))).label('rejected_images_count'),
            #func.count(case((AnnotationCameraImages.disqualified == True, 1))).label('disqualified_images_count'),
            #func.count(
            #    case(
            #        [(and_(
            #            AnnotationCameraImages.annotation_status == False,
            #            AnnotationCameraImages.disqualified == False
            #        ), 1)],
            #        else_=None
            #    )
            #).label('unannotated_images_count')
        ).filter(
            extract('year', AnnotationCameraImages.updated_at) == year,
            extract('month', AnnotationCameraImages.updated_at) == month,
            AnnotationCameraImages.annotation_user_id == user_id
        ).group_by(
            extract('day', AnnotationCameraImages.updated_at)
        ).order_by('day').all()

        # Initialize days with zeros
        days = list(range(1, days_in_month + 1))
        series = {
            "annotated_images_count": [0] * days_in_month,
            #"rejected_images_count": [0] * days_in_month,
            #"disqualified_images_count": [0] * days_in_month,
            #"unannotated_images_count": [0] * days_in_month,
        }

        # Fill values from query
        for row in daily_stats:
            day_index = int(row.day) - 1
            series["annotated_images_count"][day_index] = row.annotated_images_count
            #series["rejected_images_count"][day_index] = row.rejected_images_count
            #series["disqualified_images_count"][day_index] = row.disqualified_images_count
            #series["unannotated_images_count"][day_index] = row.unannotated_images_count

        chart_data = {
            "message": "User monthly chart fetched successfully.",
            "success": True,
            "data": {
                "series": [
                    {"name": key, "data": values}
                    for key, values in series.items()
                ],
                "categories": [str(day) for day in days]
            }
        }
        
        return chart_data
        
    except Exception as e:
        print(f"Error in get_user_summary_chart: {e}")
        return {"error": str(e)}
        
        
def get_all_users_summary():
    try:
        
        # sql = text("""
        # SELECT 
        #     aci.factory_id,
        #     aci.annotation_user_id,
        #     u.name,
        #     u.email,
        #     u.mobile_no,
        #     COUNT(*) FILTER (WHERE aci.rejected = TRUE) AS rejected_images_count,
        #     COUNT(*) FILTER (WHERE aci.disqualified = TRUE) AS disqualified_images_count,
        #     COUNT(*) FILTER (WHERE aci.annotation_status = TRUE) AS annotated_images_count,
        #     COUNT(*) FILTER (WHERE aci.annotation_status = FALSE) AS unannotated_images_count
        # FROM ann_camera_images aci
        # JOIN uam_user u ON u.user_id = aci.annotation_user_id
        # WHERE aci.annotation_user_id != 0
        # GROUP BY GROUPING SETS (
        #     (aci.factory_id, aci.annotation_user_id, u.user_id),
        #     (aci.annotation_user_id, u.user_id)
        # )
        # """)

        # results = db.session.execute(sql).fetchall()

        results = db.session.query(
            AnnotationCameraImages.job_id.label("job_id"),
            AnnotationCameraImages.annotation_user_id.label("annotation_user_id"),
            AnnotationCameraImages.review_user_id.label("review_user_id"),
            AnnotationCameraImages.client_id.label("client_id"),
            AnnotationCameraImages.factory_id.label("factory_id"),
            AnnotationCameraImages.batch_id.label("batch_id"),
            AnnotationJobAssignment.limit.label("limit"),
            AnnotationJobAssignment.status.label("status"),
            func.count().label("total_images"),
            func.sum(
                case(
                    (AnnotationCameraImages.annotation_status == True, 1),
                    else_=0
                )
            ).label("annotated_images_count"),
            func.sum(
                case(
                    ((AnnotationCameraImages.annotation_status == False) & (AnnotationCameraImages.disqualified == False), 1),
                    else_=0
                )
            ).label("unannotated_images_count"),
            func.sum(
                case(
                    (AnnotationCameraImages.disqualified == True, 1),
                    else_=0
                )
            ).label("disqualified_images_count"),
            func.sum(
                case(
                    (AnnotationCameraImages.rejected == True, 1),
                    else_=0
                )
            ).label("rejected_images_count"),
        ).join(
            AnnotationJobAssignment, AnnotationCameraImages.job_id == AnnotationJobAssignment.job_id
        ).filter(
            AnnotationCameraImages.job_id != None
        ).group_by(
            AnnotationCameraImages.job_id,
            AnnotationCameraImages.annotation_user_id,
            AnnotationCameraImages.review_user_id,
            AnnotationCameraImages.client_id,
            AnnotationCameraImages.factory_id,
            AnnotationCameraImages.batch_id,
            AnnotationJobAssignment.limit,
            AnnotationJobAssignment.status
        ).all()



        
        user_summaries = []
        for row in results:
            # user_summary = {
            #     "factory_id": row.factory_id,
            #     "factory_name": Factory.get_name_by_id(row.factory_id) if row.factory_id is not None else "ALL",
            #     "user_id": row.annotation_user_id,
            #     "name": row.name,
            #     "email": row.email,
            #     "mobile_no": row.mobile_no,
            #     "rejected_images_count": row.rejected_images_count,
            #     "disqualified_images_count": row.disqualified_images_count,
            #     "annotated_images_count": row.annotated_images_count,
            #     "unannotated_images_count": row.unannotated_images_count,
            # }
            # user_summaries.append(user_summary)

            user_summary = {
                "job_id": row.job_id,
                "annotation_user_id": row.annotation_user_id,
                "annotation_user_name": Users.get_name_by_user_id(row.annotation_user_id),
                "review_user_id": row.review_user_id,
                "review_user_name": Users.get_name_by_user_id(row.review_user_id),
                "client_id": row.client_id,
                "client_name": Client.get_name_by_id(row.client_id),
                "factory_id": row.factory_id,
                "factory_name": Factory.get_name_by_id(row.factory_id),
                "batch_id": row.batch_id,
                "status": row.status,
                "total_images": row.total_images,
                "annotated_images_count": row.annotated_images_count,
                "unannotated_images_count": row.unannotated_images_count,
                "rejected_images_count": row.rejected_images_count,
                "disqualified_images_count": row.disqualified_images_count,
            }
            user_summaries.append(user_summary)

        # print(user_summaries)

        return {"users_summary": user_summaries}

    except Exception as e:
        print(f"Error in get_all_users_summary: {e}")
        return {"error": str(e)}


def get_all_images(data):
    try:
        annotation_user_id = data.get("annotation_user_id")
        factory_id = data.get("factory_id")
        status = data.get("status", "")
        page = int(data.get("page", 1))
        per_page = int(data.get("per_page", 20))
        

        # Mandatory filters
        if not annotation_user_id or not factory_id:
            return {"error": "annotation_user_id and factory_id are required."}


        query = db.session.query(AnnotationCameraImages).filter(
            AnnotationCameraImages.annotation_user_id == annotation_user_id,
            AnnotationCameraImages.factory_id == factory_id
        )      


        # Status filters
        if status == "annotated":
            query = query.filter(AnnotationCameraImages.annotation_status == True)
        elif status == "rejected":
            query = query.filter(AnnotationCameraImages.rejected == True)
        elif status == "disqualified":
            query = query.filter(AnnotationCameraImages.disqualified == True)
        elif status == "background":
            query = query.filter(AnnotationCameraImages.background_image == True)    


        # Order and pagination
        query = query.order_by(AnnotationCameraImages.created_at.desc())
        images = query.offset((page - 1) * per_page).limit(per_page).all()

        results = []
        for record in images:
            results.append({
                "id": record.id,
                "camera_id": record.camera_id,
                "image_url": record.image_url,
                "annotation_user_id": record.annotation_user_id,
                "annotation_user_name": Users.get_name_by_user_id(record.annotation_user_id),
                "review_user_id": record.review_user_id,
                "review_user_name": Users.get_name_by_user_id(record.review_user_id),
                "annotation": record.annotation,
                "annotation_status": getattr(record, "annotation_status", None),
                "rejected": getattr(record, "rejected", None),
                "review_status": getattr(record, "review_status", None),
                "review_comments": getattr(record, "review_comments", None),
                "disqualified": getattr(record, "disqualified", None),
                "detection_model": getattr(record, "detection_model", None),
                "background_image": getattr(record, "background_image", None),
                "modules": Modules.get_records_by_list(
                    AnnotationYoloClasses.get_modules(record.client_id, record.factory_id, record.batch_id)
                ),
                "created_at": record.created_at.strftime("%Y-%m-%d") if record.created_at else None,
                "updated_at": record.updated_at.strftime("%Y-%m-%d") if record.updated_at else None
            })
        return results
    except Exception as e:
        print(f"Error in get_all_images: {e}")
        return {"error": str(e)}


