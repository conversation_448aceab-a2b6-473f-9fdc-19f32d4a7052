from api import db
import datetime
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import ARRAY

class Client(db.Model):
    __tablename__ = 'ast_client'

    client_id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.Text, nullable=False)
    address = db.Column(db.Text, nullable=False)
    logo = db.Column(db.Text, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    active = db.Column(db.Bo<PERSON>, default=True, nullable=False)
    
    @classmethod
    def get_by_id(cls, client_id):
        return cls.query.filter_by(client_id=client_id, active=True).first()

    @classmethod
    def get_name_by_id(cls, client_id):
        rec = cls.query.with_entities(cls.name).filter(cls.client_id==client_id, cls.active==True).first()
        return rec.name
    
    @classmethod
    def get_all_records(cls):
        return cls.query.filter_by(active=True).all()
    
    @classmethod
    def get_ids_by_list(cls,id_list):
        final_list=[]
        find_record=cls.query.filter(cls.client_id.in_(id_list)).all()
        if find_record:
            final_list= [x.client_id for x in find_record if x.active]
        return final_list

    @classmethod
    def get_records_by_list(cls,id_list):
        return cls.query.filter(cls.client_id.in_(id_list),cls.active==True).all()
       

    @classmethod
    def create(cls, data):
        new_rec = Client(
            name=data["name"],
            address=data["address"],
            logo=data["logo"],
            active=True,
            created_at=datetime.datetime.utcnow()
        )
        db.session.add(new_rec)
        db.session.commit()
        return new_rec

    def update(self, name=None, address=None):
        if name:
            self.name = name
        if address:
            self.address = address
                        
        self.updated_at = datetime.datetime.utcnow()
        db.session.commit()
        return self


    @classmethod
    def toggle_status(cls, client_id):
        rec = cls.query.filter_by(client_id=client_id).first()
        if rec:
            rec.active = not rec.active
            rec.updated_at = datetime.datetime.utcnow()
            db.session.commit()
            return rec
        return None
        

class Factory(db.Model):
    __tablename__ = 'ast_factory'

    factory_id = db.Column(db.Integer, primary_key=True)
    client_id = db.Column(db.Integer, db.ForeignKey('ast_client.client_id'))
    name = db.Column(db.Text, nullable=False)
    address = db.Column(db.Text, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    active = db.Column(db.Boolean, default=True, nullable=False)
    
    #Relationships
    users_factories = relationship('UserFactory', backref='factory')
    factory_areas = relationship('Area', backref='factory')


    @classmethod
    def get_by_id(cls, factory_id):
        return cls.query.filter_by(factory_id=factory_id, active=True).first() 

    @classmethod
    def get_records_by_client_id(cls,client_id):
        return cls.query.filter(cls.client_id==client_id,cls.active==True).all()
        
    @classmethod
    def get_name_by_id(cls, factory_id):
        rec = cls.query.with_entities(cls.name).filter(cls.factory_id==factory_id, cls.active==True).first()
        return rec.name
    
    @classmethod
    def get_all_records(cls):
        return cls.query.filter_by(active=True).all()
    
    @classmethod
    def get_ids_by_list(cls,id_list):
        final_list=[]
        find_record=cls.query.filter(cls.factory_id.in_(id_list)).all()
        if find_record:
            final_list= [x.factory_id for x in find_record if x.active]
        return final_list

    @classmethod
    def get_records_by_list(cls,id_list):
        return cls.query.filter(cls.factory_id.in_(id_list),cls.active==True).all()
       

    @classmethod
    def create(cls, data):
        new_rec = Factory(
            client_id=data["client_id"],
            name=data["name"],
            address=data["address"],
            active=True,
            created_at=datetime.datetime.utcnow()
        )
        db.session.add(new_rec)
        db.session.commit()
        return new_rec

    def update(self, name=None, address=None):
        if name:
            self.name = name
        if address:
            self.address = address
                        
        self.updated_at = datetime.datetime.utcnow()
        db.session.commit()
        return self

    def delete(self):
        
            for i in self.users_factories:
                db.session.delete(i)
                
            for area in self.factory_areas:
                
                for sub_area in area.sub_areas:
                    for camera in sub_area.cameras:
                        db.session.delete(camera)
                    db.session.delete(sub_area)
                
                for user_area in area.users_areas:
                    db.session.delete(user_area)
                    
                db.session.delete(area)
                    
            db.session.delete(self)
            db.session.commit()
            
    @classmethod
    def toggle_status(cls, factory_id):
        rec = cls.query.filter_by(factory_id=factory_id).first()
        if rec:
            rec.active = not rec.active
            rec.updated_at = datetime.datetime.utcnow()
            db.session.commit()
            return rec
        return None


class Area(db.Model):
    __tablename__ = 'ast_area'

    area_id = db.Column(db.Integer, primary_key=True)
    factory_id = db.Column(db.Integer, db.ForeignKey('ast_factory.factory_id'))
    name = db.Column(db.Text, nullable=False)
    address = db.Column(db.Text, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    active = db.Column(db.Boolean, default=True, nullable=False)
    
    
    users_areas = relationship('UserArea', backref='area')
    sub_areas = relationship('SubArea', backref='area')


    @classmethod
    def get_by_id(cls, area_id):
        return cls.query.filter_by(area_id=area_id, active=True).first()    
    
    @classmethod
    def get_name_by_area_id(cls, area_id):
        return cls.query.with_entities(cls.name).filter_by(area_id=area_id).first()
    
    @classmethod
    def get_area_id_by_name(cls, name):
        return cls.query.with_entities(cls.area_id).filter_by(name=name).first()

    @classmethod
    def get_area_ids_by_name_list(cls, area_names, factory_id):
        return cls.query.with_entities(cls.area_id).filter(cls.name.in_(area_names),cls.factory_id==factory_id).all()
    
    @classmethod
    def get_dict_of_area_ids_by_name_list(cls, area_names, factory_id):
        result = cls.query.with_entities(cls.name, cls.area_id).filter(
            cls.name.in_(area_names),
            cls.factory_id == factory_id
        ).all()
        return {name: area_id for name, area_id in result}

    @classmethod
    def get_all_records(cls):
        return cls.query.filter_by(active=True).all()
    
    @classmethod
    def get_total_records(cls):
        return cls.query.all()
    
    @classmethod
    def get_records_by_list(cls,ids_list):
        return cls.query.filter(cls.area_id.in_(ids_list), cls.active==True).all()
    
    @classmethod
    def get_records_by_factory_ids(cls,ids_list):
        return cls.query.filter(cls.factory_id.in_(ids_list), cls.active==True).all()
    
    @classmethod
    def get_records_by_factory_id(cls,factory_id):
        return cls.query.filter(cls.factory_id ==factory_id , cls.active==True).all()
    
    @classmethod
    def get_records_by_factory_and_area(cls,area_names,factory_id):
        print("names: ", area_names) 
        return cls.query.with_entities(cls.area_id).filter(cls.factory_id ==factory_id , cls.active==True, cls.name.in_(area_names)).all() 
    
    
    @classmethod
    def get_ids_by_list(cls,id_list):
        final_list=[]
        find_record=cls.query.filter(cls.area_id.in_(id_list)).all()
        if find_record:
            final_list= [x.area_id for x in find_record if x.active]
        return final_list
    
    @classmethod
    def create(cls, data):
        new_rec = cls(
            name=data["name"],
            address=data["address"],
            factory_id=data["factory_id"],
            active=True,
            created_at=datetime.datetime.utcnow()
        )
        db.session.add(new_rec)
        db.session.commit()
        return new_rec

    def update(self, name=None, address=None, factory_id=None):
        if name:
            self.name = name
        if address:
            self.address = address
        if self.factory_id:
            self.factory_id= factory_id
                        
        self.updated_at = datetime.datetime.utcnow()
        db.session.commit()
        return self

    def delete(self):
            for i in self.users_areas:
                db.session.delete(i)
                
            for sub_area in self.sub_areas:
                for camera in sub_area.cameras:
                    db.session.delete(camera)
                db.session.delete(sub_area)
                
            db.session.delete(self)
            db.session.commit()
            
    @classmethod
    def toggle_status(cls, id):
        rec = cls.query.filter_by(area_id=id).first()
        if rec:
            rec.active = not rec.active
            rec.updated_at = datetime.datetime.utcnow()
            db.session.commit()
            return rec
        return None




            
class SubArea(db.Model):
    __tablename__ = 'ast_subarea'

    sub_area_id = db.Column(db.Integer, primary_key=True)
    area_id = db.Column(db.Integer, db.ForeignKey('ast_area.area_id'))
    name = db.Column(db.Text, nullable=False)
    address = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    active = db.Column(db.Boolean, default=True, nullable=False)
    

    @classmethod
    def get_by_id(cls, sub_area_id):
        return cls.query.filter_by(sub_area_id=sub_area_id, active=True).first()    
    
    @classmethod
    def get_all_records(cls):
        return cls.query.filter_by(active=True).all()
    
    @classmethod
    def get_records_by_list(cls,ids_list):
        return cls.query.filter(cls.sub_area_id.in_(ids_list),cls.active == True).all()
    
    @classmethod
    def get_records_by_area_list(cls,area_ids):
        return cls.query.filter(cls.area_id.in_(area_ids),cls.active == True).all()
    
    @classmethod
    def get_records_by_area_id(cls,area_id): 
        return cls.query.filter(cls.area_id == area_id,cls.active == True).all()
    
    
    @classmethod
    def get_ids_by_list(cls,id_list):
        final_list=[]
        find_record=cls.query.filter(cls.sub_area_id.in_(id_list)).all()
        if find_record:
            final_list= [x.sub_area_id for x in find_record if x.active]
        return final_list

    
    @classmethod
    def create(cls, data):
        new_rec = cls(
            name=data["name"],
            address=data["address"],
            area_id=data["area_id"],
            active=True,
            created_at=datetime.datetime.utcnow()
        )
        db.session.add(new_rec)
        db.session.commit()
        return new_rec

    @classmethod
    def create_list(cls, data):
        result=[]
        for area in data["areas"]:
            for sub in area["sub_areas"]:
                result.append(cls(
                    name=sub,
                    area_id=area["area_id"],
                    active=True,
                    created_at=datetime.datetime.utcnow()
                ))
            
        db.session.add_all(result)
        db.session.commit()
        return True


    def update(self, name=None,  address=None):
        if name:
            self.name = name
        if address:
            self.address = address
                        
        self.updated_at = datetime.datetime.utcnow()
        db.session.commit()
        return self

    def delete(self):
            for camera in self.cameras:
                db.session.delete(camera)
            db.session.delete(self)
            db.session.commit()
            
    @classmethod
    def toggle_status(cls, id):
        rec = cls.query.filter_by(sub_area_id=id).first()
        if rec:
            rec.active = not rec.active
            rec.updated_at = datetime.datetime.utcnow()
            db.session.commit()
            return rec
        return None
        


class Cameras(db.Model):
    __tablename__ = 'ast_cameras'
    camera_id = db.Column(db.Text, primary_key=True, unique=True)
    client_id = db.Column(db.Integer,  db.ForeignKey('ast_client.client_id'))
    factory_id = db.Column(db.Integer,  db.ForeignKey('ast_factory.factory_id'))
    area_id = db.Column(db.Integer,  db.ForeignKey('ast_area.area_id'))
    sub_area_id = db.Column(db.Integer, db.ForeignKey('ast_subarea.sub_area_id'))
    
    camera_ip = db.Column(db.Text, nullable=False)
    camera_name = db.Column(db.Text, )
    camera_position_no = db.Column(db.Text,)
    
    nvr_no = db.Column(db.Integer)
    username = db.Column(db.Text)
    password = db.Column(db.Text)
    stream = db.Column(db.Text)
    port = db.Column(db.Integer)
   
    modules = db.Column(ARRAY(db.Integer))
    created_at = db.Column(db.DateTime, default=datetime.datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    active = db.Column(db.Boolean, default=True, nullable=False)
    
    @classmethod
    def get_by_id(cls, camera_id):
        return cls.query.filter_by(camera_id=camera_id, active=True).first()
        
    @classmethod
    def get_by_ip(cls, camera_ip):
            return cls.query.filter_by(camera_ip=camera_ip, active=True).first()
       
    @classmethod
    def get_active_camera_ids(cls):
        return cls.query.with_entities(cls.camera_id).filter_by(active=True).all() 

    @classmethod
    def get_name_by_camera_id(cls, camera_id):
        return cls.query.with_entities(cls.camera_name).filter_by(camera_id=camera_id, active=True).first()    

    @classmethod
    def get_all_records(cls):
        return cls.query.filter_by(active=True).all()
    
    @classmethod
    def get_active_inactive_records(cls):
        return cls.query.all()
    
    @classmethod
    def get_records_by_area_list(cls,area):
        return cls.query.filter(cls.sub_area_id.in_(area)).all()
    
    @classmethod
    def get_records_module_wise(cls): 
        return cls.query(
            func.unnest(Cameras.modules).label('module_id'),
            func.count().label('camera_count')).group_by('module_id').order_by('module_id').all()
    
    @classmethod
    def get_records_by_sub_area_list(cls,sub_areas_ids):
        return cls.query.filter(cls.sub_area_id.in_(sub_areas_ids), cls.active==True).all() 

    @classmethod
    def get_modules_by_cameraID(cls, cameraID):
        result = cls.query.with_entities(cls.modules).filter_by(camera_id=cameraID).first()
        modules_list = []
        if result:
            modules_list = result.modules
            modules_list.sort

        return modules_list
    
    @classmethod
    def create(cls, data, modules):
        new_rec = cls(
            camera_id=data["camera_id"].strip(),
            camera_ip=data["camera_ip"],
            camera_name=data["camera_name"],
            camera_position_no=data["camera_position_no"],
            nvr_no=data["nvr_no"],
            username=data["username"],
            password=data["password"],
            stream=data["stream"],
            port=data["port"],
            modules=modules,
            client_id=data["client_id"],
            factory_id=data["factory_id"],
            area_id=data["area_id"],
            sub_area_id=data["sub_area_id"],
            active=True,
            created_at=datetime.datetime.utcnow()
        )
        db.session.add(new_rec)
        db.session.commit()
        return new_rec

    @classmethod
    def create_bulk(cls, cameras_list, status=None):
        add_cameras=[]
        for data in cameras_list:
            camera_id = data["camera_id"].strip()

            # Try to find an existing camera by camera_id
            existing_camera = cls.query.filter_by(camera_id=camera_id).first()

            if existing_camera:
                # Update fields
                existing_camera.camera_ip = data["camera_ip"]
                existing_camera.camera_name = data["camera_name"]
                existing_camera.camera_position_no = data["camera_position_no"]
                existing_camera.nvr_no = data["nvr_no"]
                existing_camera.username = data["username"]
                existing_camera.password = data["password"]
                existing_camera.stream = data["stream"]
                existing_camera.port = data["port"]
                existing_camera.modules = data["modules"]
                existing_camera.client_id = data["client_id"]
                existing_camera.factory_id = data["factory_id"]
                existing_camera.area_id = data["area_id"]
                existing_camera.sub_area_id = data["sub_area_id"]
                existing_camera.updated_at = datetime.datetime.utcnow()
                existing_camera.active = True
                db.session.commit()
            else:
                add_cameras.append(cls(
                    camera_id=camera_id,
                    camera_ip=data["camera_ip"],
                    camera_name=data["camera_name"],
                    camera_position_no=data["camera_position_no"],
                    nvr_no=data["nvr_no"],
                    username=data["username"],
                    password=data["password"],
                    stream=data["stream"],
                    port=data["port"],
                    modules=data["modules"],
                    client_id=data["client_id"],
                    factory_id=data["factory_id"],
                    area_id=data["area_id"],
                    sub_area_id=data["sub_area_id"],
                    active=True,
                ))
        db.session.add_all(add_cameras)
        db.session.commit()
        
        
    def update(self, data, modules):
        if data["change_camera_id"]:
            self.camera_id = data["new_camera_id"]
        if "camera_ip" in data and data["camera_ip"]:
            self.camera_ip = data["camera_ip"]
        if "modules" in data and len(modules) > 0:
            self.modules = modules
        if "sub_area_id" in data and data["sub_area_id"]:
            self.sub_area_id = data["sub_area_id"]
        if "camera_name" in data and data["camera_name"]:
            self.camera_name = data["camera_name"]
        if "camera_position_no" in data and data["camera_position_no"]:
            self.camera_position_no = data["camera_position_no"]
        if "nvr_no" in data and data["nvr_no"]:
            self.nvr_no = data["nvr_no"]
        if "username" in data and data["username"]:
            self.username = data["username"]
        if "password" in data and data["password"]:
            self.password = data["password"]
        if "stream" in data and data["stream"]:
            self.stream = data["stream"]
        if "port" in data and data["port"]:
            self.port = data["port"]
        self.updated_at = datetime.datetime.utcnow()
        db.session.commit()
        return self

    def delete(self):
        if self.image:
            self.image.delete()
            
        db.session.delete(self)
        db.session.commit()
            
    @classmethod
    def toggle_status(cls, id):
        rec = cls.query.filter_by(camera_id=id).first()
        if rec:
            rec.active = not rec.active
            db.session.commit()
            return rec
        return None
    

class CamerasliveFeed(db.Model):
    __tablename__ = 'ast_camera_livefeed'
    id = db.Column(db.Integer, primary_key=True)
    client_id = db.Column(db.Integer, db.ForeignKey('ast_client.client_id'))
    factory_id = db.Column(db.Integer, db.ForeignKey('ast_factory.factory_id'))
    camera_id = db.Column(db.Text, db.ForeignKey('ast_cameras.camera_id'))
    image_url = db.Column(db.Text, nullable=False)
    timestamp = db.Column(db.DateTime, default=datetime.datetime.utcnow)
    
    @classmethod
    def create_update(cls, data):
        rows = []
        try:
            updated = False
            for item in data:
                client_id = item.get("client_id")
                factory_id = item.get("factory_id")
                camera_id = item.get("camera_id")
                image_url = item.get("image_url")
                timestamp = item.get("timestamp")
                print("Processing:", camera_id, client_id, factory_id)
                existing_record = cls.query.filter(
                    (cls.camera_id == camera_id) &
                    (cls.client_id == client_id) &
                    (cls.factory_id == factory_id)
                ).first()
                print("Existing record:", existing_record)
                if existing_record:
                    existing_record.image_url = image_url
                    existing_record.timestamp = timestamp
                    updated = True
                else:
                    new_record = cls(
                        client_id=client_id,
                        factory_id=factory_id,
                        camera_id=camera_id,
                        image_url=image_url,
                        timestamp=timestamp
                    )
                    rows.append(new_record)

            if rows:
                db.session.add_all(rows)
            if rows or updated:
                db.session.commit()

        except Exception as e:
            db.session.rollback()
            raise Exception(f"Error Uploading camera livefeed: {e}")
    # @classmethod
    # def create_update(cls, data):
    #     rows = []
    #     try:
    #         for item in data:
    #             client_id = item.get("client_id")
    #             factory_id = item.get("factory_id")
    #             camera_id = item.get("camera_id")
    #             image_url = item.get("image_url")
    #             timestamp=item.get("timestamp")
                
    #             existing_record = cls.query.filter((cls.camera_id == camera_id)&(cls.client_id == client_id)&(cls.factory_id == factory_id)).first()
    #             print("exitsting_recordssssssss",existing_record)
    #             if existing_record:
    #                 existing_record.image_url=image_url
    #                 existing_record.timestamp=timestamp
    #             else:
    #                 # Create a new record
    #                 new_record = cls(
    #                     client_id=client_id,
    #                     factory_id=factory_id,
    #                     camera_id=camera_id,
    #                     image_url=image_url, 
    #                     timestamp=timestamp  
    #                 )
    #                 rows.append(new_record)

    #         if rows:
    #             db.session.add_all(rows)
    #             db.session.commit()
        
    #     except Exception as e:
    #         db.session.rollback()
    #         raise Exception(f"Error Uploading camera livefeed: {e}")
            
    
    @classmethod
    def get_by_camera_id(cls, camera_id, client_id, factory_id):
        return cls.query.filter_by(camera_id=camera_id , client_id=client_id, factory_id=factory_id).first()
    
