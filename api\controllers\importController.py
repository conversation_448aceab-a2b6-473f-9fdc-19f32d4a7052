import os
import calendar
import requests
import yaml

from api.utils.utils import *
from api.utils.gcp_storage import *
from api import db

from api.models.uamModels import *
from api.models.annotationModels import *
from api.models.assetModels import *
from api.models.hseModels import *

from sqlalchemy import and_, or_, func, case, extract
from sqlalchemy.dialects.postgresql import ARRAY

from datetime import datetime, timedelta
from collections import defaultdict

def import_cameras(data, client, factory):
    
    import_url = data['import_url']
    
    print("factory_name:", factory.name)
    #JSON data to send
    data = {
       "factory_name": factory.name
    }

     # Send PUT request
    response = requests.put(import_url, json=data)

    #print("response:", response.json())
    
    # Check if response contains camera data
    if 'data' in response.json() and isinstance(response.json()['data'], list):
        cameras_list = []
        
        for camera_data in response.json()['data']:
            
            print(camera_data)
            
            # Get module IDs from module names
            module_names = camera_data.get('modules', [])
            module_ids = []
            
            for module_name in module_names:
                module = Modules.query.filter_by(name=module_name).first()
                if module:
                    module_ids.append(module.module_id)
            
            print(module_ids)
            
            # Create camera data dictionary
            camera = {
                "camera_id": camera_data.get('camera_id'),
                "camera_ip": camera_data.get('camera_ip'),
                "camera_name": camera_data.get('camera_name'),
                "camera_position_no": camera_data.get('camera_position_no'),
                "nvr_no": camera_data.get('nvr_no', 0),
                "username": camera_data.get('username'),
                "password": camera_data.get('password'),
                "stream": camera_data.get('stream'),
                "port": camera_data.get('port'),
                "modules": module_ids,
                "client_id": client.client_id,
                "factory_id": factory.factory_id,
                "area_id": 1,
                "sub_area_id": 1,
                "active": True
            }
            cameras_list.append(camera)
        
        # Bulk insert cameras into database
        if cameras_list:
            Cameras.create_bulk(cameras_list)
            
        return {"message": "Cameras imported successfully."}
    else:
        return {"message": "No camera data found in response"}
    

