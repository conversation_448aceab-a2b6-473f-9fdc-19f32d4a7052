import jwt
import zipfile
import os
import shutil
from datetime import datetime, timedelta
import calendar
from api import app
from api.models.assetModels import Area, SubArea
from api import db

roles={
    "admin": "admin",
    "global": "global",
    "it": "it",
    "factory": "factory",
    "area": "area",
    "tech_qa": "tech_qa",
}


ALLOWED_EXTENSIONS = set(["jpg", "jpeg", "png"])

def allowed_file(filename):
    return "." in filename and filename.rsplit(".", 1)[1].lower() in ALLOWED_EXTENSIONS


def is_number(value):
    try:
        int(value)
        return True
    except ValueError:
        return False

def is_string(value):
    try:
        str(value)
        return True
    except ValueError:
        return False

def snake_case(input_str: str) -> str:
    return input_str.strip().lower().replace(" ", "_")

def generate_jwt_token(data):
    token = jwt.encode(
        {
            "user_id": data['user_id'],
            "factory_id": data['factory_id'],
            "client_id": data['client_id'],
            "role_name": data['role_name'],
            "role_id": data['role_id'],
            "exp": datetime.utcnow() + timedelta(hours=24),
        },
        app.config["SECRET_KEY"],
        algorithm="HS256",
    )
    return token
    
def zip_folder(folder_path, output_path):
    with zipfile.ZipFile(output_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, _, files in os.walk(folder_path):
            for file in files:
                file_path = os.path.join(root, file)
                # Preserve folder structure
                arcname = os.path.relpath(file_path, start=folder_path)
                zipf.write(file_path, arcname)
                
def get_file_size(file_path):
    zip_size = os.path.getsize(file_path)

    # Convert and print the size appropriately
    if zip_size < 1024:
        size_string = f"{zip_size} B"
    elif zip_size < 1024 * 1024:
        size_string = f"{zip_size / 1024:.2f} KB"
    else:
        size_string = f"{zip_size / (1024 * 1024):.2f} MB"

    return size_string
    
def delete_folder(folder_path):
    if os.path.exists(folder_path) and os.path.isdir(folder_path):
        shutil.rmtree(folder_path)
        return True
    else:
        return False
        
def delete_file(file_path):
    if os.path.exists(file_path) and os.path.isfile(file_path):
        os.remove(file_path)
        return True
    else:
        return False
        
        
def get_week_dates_obj(week):
    # Parse the year and week number
    year, week_num = week.split('-W')
    year = int(year)
    week_num = int(week_num)
    
    # Calculate the start date of the ISO week
    start_date_obj = datetime.strptime(f'{year}-W{week_num:02d}-1', "%G-W%V-%u")
    # Calculate the end date of the ISO week
    end_date_obj = start_date_obj + timedelta(days=6)
    
    return start_date_obj.replace(hour=0, minute=0, second=0), end_date_obj.replace(hour=23, minute=59, second=59)

def get_month_dates_obj(month):
    # Parse the year and month number
    year, month_num = month.split('-')
    year = int(year)
    month_num = int(month_num)
    
    # Get the first and last days of the month
    start_date_obj = datetime(year, month_num, 1)
    last_day = calendar.monthrange(year, month_num)[1]
    end_date_obj = datetime(year, month_num, last_day)
    
    return start_date_obj.replace(hour=0, minute=0, second=0), end_date_obj.replace(hour=23, minute=59, second=59)

def get_all_sub_areas_by_factory(factory_id):
    # Start query with a join to include factory_id filtering
    query = db.session.query(SubArea).join(Area, SubArea.area_id == Area.area_id)

    # Apply factory_id filter
    query = query.filter(Area.factory_id == factory_id, SubArea.active == True)

    # Fetch the matching sub-areas
    sub_areas = query.all()

    # Collect and return only the sub_area IDs
    filtered_sub_area_ids = [sub_area.sub_area_id for sub_area in sub_areas]

    return filtered_sub_area_ids


#---------- Get start_date and end_date datewise ----------------
def get_start_and_end_datewise(date):
    """
    Get the start and end datetime objects for a specific date.

    Args:
        date (str): A string representing the date in "YYYY-MM-DD" format.

    Returns:
        tuple: (start_date, end_date) as datetime objects.
    """
    try:
        start_date = datetime.strptime(date, "%Y-%m-%d")
        end_date = start_date.replace(hour=23, minute=59, second=59)
        return start_date, end_date
    except ValueError as e:
        raise ValueError("Incorrect date format. Expected 'YYYY-MM-DD'.") from e


# -------------------------- Parse Filters Function ------------------------------------
def parse_filters(data):
    """
    Parse date filters from the request data and return start_date and end_date.
    
    Args:
        data: Dictionary containing filters with date-related parameters
        
    Returns:
        tuple: (start_date, end_date) as datetime objects
    """
    filters = data.get("filters", {})
    # print("filters: ", filters)
    date = filters.get("date", "")
    week = filters.get("week", "")
    month = filters.get("month", "")
    starting = filters.get("starting", "")
    ending = filters.get("ending", "")
    
    if week:
        # print("week filter: data ",get_week_dates_obj(week) )
        return get_week_dates_obj(week)
    elif month:
        # print("month filter: data ",get_month_dates_obj(month) )
        return get_month_dates_obj(month)
    elif date:
        # print("date filter: data ",get_start_and_end_datewise(date) )
        return get_start_and_end_datewise(date)
    elif starting and ending:
        #     start_date = datetime.strptime(starting, "%Y-%m-%d")
    #     end_date = datetime.strptime(ending, "%Y-%m-%d").replace(hour=23, minute=59, second=59)
        return starting, ending
    else:
        # Default to current day
        today = datetime.now().date() # current date 
        start_date = datetime.combine(today, datetime.min.time()) # sets time to start of day (00:00:00)
        end_date = datetime.combine(today, datetime.max.time()) # sets time to end of day (23:59:59)
        return start_date, end_date
    

def adjust_shift_times(start_date, end_date):
    """
    Adjusts start and end times based on day-specific business hours:
    Mon-Fri: 8:00 - 21:00 (fixed)
    Sat: 10:00 - 16:00
    Sun: 10:00 - 15:00

    Args:
        start_date (datetime): Original start datetime
        end_date (datetime): Original end datetime

    Returns:
        tuple: (adjusted_start_date, adjusted_end_date)
    """
    # Get day of week (0 = Monday, 6 = Sunday)
    day_of_week = start_date.weekday()
    
    # Set shift times based on weekday/weekend
    if day_of_week < 5:  # Monday to Friday
        shift_start = 8
        shift_end = 21
    elif day_of_week == 5:  # Saturday
        shift_start = 10
        shift_end = 16
    else:  # Sunday
        shift_start = 10
        shift_end = 15

    # Adjust start time
    adjusted_start = start_date.replace(
        hour=shift_start,
        minute=0,
        second=0
    )

    # Adjust end time
    adjusted_end = end_date.replace(
        hour=shift_end,
        minute=0,
        second=0
    )

    return adjusted_start, adjusted_end


def convert_hours_to_hours_minutes(hours):
    """Convert decimal hours to hours and minutes format"""
    total_minutes = hours * 60
    return f"{total_minutes // 60}h {total_minutes % 60:.2f}m"


