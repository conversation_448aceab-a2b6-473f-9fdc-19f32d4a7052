from api.models.assetModels import Client, Factory, Cameras, CamerasliveFeed
from api.models.facialModels import *
from api import db
# from sqlalchemy import and_, or_, desc
from datetime import datetime, timedelta
from api.utils.utils import parse_filters


def get_live_logs(data):
    """
    Get live facial recognition logs based on filters

    Args:
        data (dict): Contains filter parameters like client_id, factory_id, etc.

    Returns:
        list: List of facial recognition logs
    """
    client_id = data.get("client_id")
    factory_id = data.get("factory_id")
    limit = data.get("limit", 20)
    camera_id = data.get("camera_id")
    employee_id = data.get("employee_id")

    # Use parse_filters to get start_date and end_date from filters
    start_date, end_date = parse_filters(data)

    # Start with base query
    query = FacialRecognitionLogs.query.filter_by(
        client_id=client_id,
        factory_id=factory_id
    )

    # Apply additional filters if provided
    if camera_id:
        query = query.filter_by(camera_id=camera_id)

    if employee_id:
        query = query.filter_by(employee_id=employee_id)

    # Apply date range filter
    query = query.filter(
        FacialRecognitionLogs.detection_datetime >= start_date,
        FacialRecognitionLogs.detection_datetime <= end_date
    )

    # Order by detection time, most recent first
    query = query.order_by(FacialRecognitionLogs.detection_datetime.desc())

    # Limit the number of results
    logs = query.limit(limit).all()

    # Format the results
    result = []
    for log in logs:
        camera_obj = Cameras.get_by_id(log.camera_id)
        employee_obj = FacialEmployees.get_by_employee_id(log.employee_id)
        result.append({
            "log_id": log.log_id,
            "client_id": log.client_id,
            "client_name": Client.get_name_by_id(log.client_id),
            "factory_id": log.factory_id,
            "factory_name": Factory.get_name_by_id(log.factory_id),
            "employee_id": log.employee_id,
            "employee_name": employee_obj.name if employee_obj else None,
            "blacklisted": employee_obj.blacklisted if employee_obj else None,
            "employee_designation": employee_obj.designation if employee_obj else None,
            "camera_id": log.camera_id,
            "camera_name": camera_obj.camera_name if camera_obj else None,
            "camera_ip": camera_obj.camera_ip if camera_obj else None,
            "camera_position_no": camera_obj.camera_position_no if camera_obj else None,
            "detection_datetime": log.detection_datetime.strftime("%Y-%m-%d %H:%M:%S"),
            "person_detected": log.person_detected,
            "sop_followed": log.sop_followed,
            "face_image_url": log.face_image_url,
            "confidence": log.confidence if hasattr(log, 'confidence') else None
        })

    return result



def get_stats(data):
    client_id = data.get("client_id")
    factory_id = data.get("factory_id")
    camera_id = data.get("camera_id")
    employee_id = data.get("employee_id")

    # Parse date filters using the utility function
    start_date, end_date = parse_filters(data)
    print("start_date: ", start_date, "end_date: ", end_date)

    # Base query
    query = FacialRecognitionLogs.query.filter_by(
        client_id=client_id,
        factory_id=factory_id
    )

    if camera_id:
        query = query.filter_by(camera_id=camera_id)
    if employee_id:
        query = query.filter_by(employee_id=employee_id)

    # Apply date range filter
    query = query.filter(
        FacialRecognitionLogs.detection_datetime >= start_date,
        FacialRecognitionLogs.detection_datetime <= end_date
    )

    logs = query.all()

    # Prepare camera_id to position mapping
    camera_ids = list(set([log.camera_id for log in logs]))
    cameras = Cameras.query.filter(Cameras.camera_id.in_(camera_ids)).all()
    camera_position_map = {cam.camera_id: cam.camera_position_no for cam in cameras}

    total_entries = 0
    total_exits = 0
    total_unknown_alerts = 0
    sop_followed = 0
    sop_unfollowed = 0

    for log in logs:
        position = camera_position_map.get(log.camera_id, "").lower() if camera_position_map.get(log.camera_id) else ""
        if position == "gate in":
            total_entries += 1
        elif position == "gate out":
            total_exits += 1

        if getattr(log, "person_detected", "").lower() == "unknown":
            total_unknown_alerts += 1

        if getattr(log, "sop_followed", None) is True:
            sop_followed += 1
        elif getattr(log, "sop_followed", None) is False:
            sop_unfollowed += 1

    # Contractors: all employees except those with employee_id == "LAB_Unknown"
    contractors_count = FacialEmployees.query.filter(
        FacialEmployees.client_id == client_id,
        FacialEmployees.factory_id == factory_id,
        FacialEmployees.employee_id != "LAB_Unknown"
    ).count()

    return {
        "total_entries": total_entries,
        "total_exits": total_exits,
        "contractors": contractors_count,
        "total_unknown_alerts": total_unknown_alerts,
        "sop_followed": sop_followed,
        "sop_unfollowed": sop_unfollowed
    }
    

def get_daily_movement_summary(data):
    """
    Get daily movement summary with entries, exits, unknown alerts, and currently inside count
    
    Args:
        data (dict): Contains filter parameters like client_id, factory_id, filters, etc.
    
    Returns:
        dict: Daily movement summary data
    """
    client_id = data.get("client_id")
    factory_id = data.get("factory_id")
    
    # Parse date filters using the utility function
    start_date, end_date = parse_filters(data)
    
    # Base query for facial recognition logs
    query = FacialRecognitionLogs.query.filter_by(
        client_id=client_id,
        factory_id=factory_id
    ).filter(
        FacialRecognitionLogs.detection_datetime >= start_date,
        FacialRecognitionLogs.detection_datetime <= end_date
    )
    
    logs = query.all()
    
    # Get camera position mapping
    camera_ids = list(set([log.camera_id for log in logs]))
    cameras = Cameras.query.filter(Cameras.camera_id.in_(camera_ids)).all()
    camera_position_map = {cam.camera_id: cam.camera_position_no for cam in cameras}
    
    # Group data by day of week
    daily_summary = {}
    
    for log in logs:
        day_name = log.detection_datetime.strftime("%A")
        
        if day_name not in daily_summary:
            daily_summary[day_name] = {
                "day": day_name,
                "unknown_alerts": 0,
                "total_entries": 0,
                "total_exits": 0,
            }
        
        # Count unknown alerts
        if getattr(log, "person_detected", "").lower() == "unknown":
            daily_summary[day_name]["unknown_alerts"] += 1
        
        # Count entries and exits based on camera position
        position = camera_position_map.get(log.camera_id, "").lower() if camera_position_map.get(log.camera_id) else ""
        if position == "gate in":
            daily_summary[day_name]["total_entries"] += 1
        elif position == "gate out":
            daily_summary[day_name]["total_exits"] += 1
    
    # Calculate currently inside for each day
    for day_name in daily_summary:
        entries = daily_summary[day_name]["total_entries"]
        exits = daily_summary[day_name]["total_exits"]
        daily_summary[day_name]["currently_inside"] = max(0, entries - exits)
    
    # Convert to list and ensure all days of week are present
    weekdays = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"]
    result = []
    
    for day in weekdays:
        if day in daily_summary:
            result.append(daily_summary[day])
        else:
            result.append({
                "day": day,
                "unknown_alerts": 0,
                "total_entries": 0,
                "total_exits": 0,
                "currently_inside": 0
            })
    
    return result    

def camera_status(data):
    client_id = data.get("client_id")
    factory_id = data.get("factory_id")
    camera_id = data.get("camera_id")  # Optional filter
    employee_id = data.get("employee_id")  # Optional filter
    print("camera_id: ", data)
    # Parse date filters using the utility function
    start_date, end_date = parse_filters(data)
    print("start_date: ", start_date, "end_date: ", end_date)

    # Step 1: Get all livefeed cameras for this client/factory
    livefeed_query = CamerasliveFeed.query.filter_by(client_id=client_id, factory_id=factory_id)
    print('livefeed_query: ', livefeed_query)
    if camera_id:
        livefeed_query = livefeed_query.filter_by(camera_id=camera_id)
    livefeed_cameras = livefeed_query.all()
    print("livefeed_cameras: ", livefeed_cameras)
    camera_status_list = []

    for livefeed in livefeed_cameras:
        cam = Cameras.query.filter_by(camera_id=livefeed.camera_id).first()
        if not cam:
            continue

        position = (cam.camera_position_no or "")
        cam_id = cam.camera_id

        # Step 2: Get logs for this camera and position
        logs_query = FacialRecognitionLogs.query.filter(
            FacialRecognitionLogs.client_id == client_id,
            FacialRecognitionLogs.factory_id == factory_id,
            FacialRecognitionLogs.camera_id == cam_id,
            FacialRecognitionLogs.detection_datetime >= start_date,
            FacialRecognitionLogs.detection_datetime <= end_date
        )
        if employee_id:
            logs_query = logs_query.filter(FacialRecognitionLogs.employee_id == employee_id)

        logs = logs_query.all()

        total_logs = len(logs)
        blacklisted = 0
        sop_followed = 0
        sop_unfollowed = 0
        total_violations = 0

        for log in logs:
            emp = FacialEmployees.query.filter_by(employee_id=log.employee_id).first()
            if emp and getattr(emp, "blacklisted", False):
                blacklisted += 1
            if getattr(log, "sop_followed", None) is True:
                sop_followed += 1
            elif getattr(log, "sop_followed", None) is False:
                sop_unfollowed += 1
            if log.employee_id == "LAB_Unknown":
                total_violations += 1

        # Case-insensitive gate type
        gate_type = "Other"
        if position.strip().lower() == "gate in":
            gate_type = "Gate In"
        elif position.strip().lower() == "gate out":
            gate_type = "Gate Out"

        camera_status_list.append({
            "camera_id": cam_id,
            "camera_name": cam.camera_name,
            "camera_position_no": cam.camera_position_no,
            "total_logs": total_logs,
            "blacklisted": blacklisted,
            "sop_followed": sop_followed,
            "sop_unfollowed": sop_unfollowed,
            "total_violations": total_violations,
            "gate_type": gate_type,
            "image_url": getattr(livefeed, "image_url", None)
        })
        print("camera_status_list: ", camera_status_list)

    # Always return all cameras, even if only gate_in or gate_out exists
    gate_in_stats = [c for c in camera_status_list if c["gate_type"] == "Gate In"]
    gate_out_stats = [c for c in camera_status_list if c["gate_type"] == "Gate Out"]

    return {
        "gate_in": gate_in_stats,
        "gate_out": gate_out_stats
    }
    
    
    
    
