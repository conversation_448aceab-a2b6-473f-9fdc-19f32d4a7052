from flask_restx import Resource, abort
from api.models.assetModels import *
from api.models.facialModels import *
from api.models.uamModels import *

from api.payloads.facialPayloads import *
from api import facial_ns
from api.controllers import facialController as facial_func

from api import db, mqtt_client
from api.services import token_required, user_token_required
from sqlalchemy import and_, or_
import json
import re

@facial_ns.route("/sync_rows")
@facial_ns.doc("Sync Rows")
class FacialSyncRows(Resource):
    @facial_ns.doc("Sync Rows")
    @facial_ns.expect(facial_sync_payload_model)
    @facial_ns.response(200, "Rows Synced successfully.")
    @facial_ns.response(400, "Validation Error")
    def post(self):
        data = facial_ns.payload
        try:
            models=[FacialRecognitionLogs, FacialEmployeeEmbeddings, FacialEmployees] 
            for field in models:
                if field.__tablename__ in data.keys():
                    rows = data[field.__tablename__]
                    any_rows_added = field.add_multiple_rows(rows) 
                    
             # Generate the appropriate message
            if any_rows_added:
                message = "Rows Synced successfully."
            else:
                message = "No rows were added."
            
            response = {"message": message}  
                
            return response, 200 

        except Exception as e:
            db.session.rollback()  # Rollback any failed transactions
            print(str(e))
            abort(400, {
                "success": False,
                "message": "Failed to sync rows",
                "error": str(e)
            })
            
            
@facial_ns.route("/get_db_id/<string:client_id>/<string:factory_id>/<string:table_name>")
@facial_ns.doc("Get DB ID")
class FacialGetDBID(Resource):
    # @token_required
    @facial_ns.doc("Get DB ID")
    @facial_ns.response(200, "DB ID fetched successfully.")
    @facial_ns.response(400, "Validation Error")
    # @sub_area_ns.doc(security=["Authorization", "Token-Type"])
    def get(self,client_id, factory_id, table_name):
        try:
            if client_id and factory_id and table_name: 
                models=[FacialRecognitionLogs, FacialEmployeeEmbeddings, FacialEmployees] 
                get_table= [x for x in models if x.__tablename__.lower() == table_name.lower()]
                print("got table: ", get_table)
                if get_table and get_table[0]:
                    
                    resp = get_table[0].get_local_db_id(client_id=client_id, factory_id=factory_id) 
                    table= get_table[0].__tablename__
                    print("Resp: ", resp)
                    
                    return {
                        "message": "DB ID fetched successfully" if resp is not None else "ID does not exist",
                        "success":  True  if resp is not None else False,
                        "id": resp  if resp is not None else 0, 
                        "table": table
                        }, 200
                else:
                    return {
                    "message": "Table not found" ,
                    "success":  False,
                    }, 400
            else:
                return {
                "message": "Insufficient data" ,
                "success":  False,
                }, 400
                
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})
            
            
@facial_ns.route("/get_live_logs")
@facial_ns.doc("Get Live Facial Logs")
class FacialGetLiveLogs(Resource):
    @facial_ns.doc("Get Live Facial Logs")
    @facial_ns.expect(facial_live_logs_payload)
    @facial_ns.response(200, "Logs fetched successfully.")
    @facial_ns.response(400, "Validation Error")
    def put(self):
        data = facial_ns.payload
        try:
            req_fields = ["client_id", "factory_id"]
            for field in req_fields:
                if field not in data or not data[field]:
                    return {"message": f"{field} is missing."}, 400
            
            logs = facial_func.get_live_logs(data)
            
            return {
                "message": "Facial recognition logs fetched successfully.",
                "success": True,
                "data": logs
            }, 200
            
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})
            
            


@facial_ns.route("/get_stats")
@facial_ns.doc("Get Stats")
class FacialGetLiveLogs(Resource):
    @facial_ns.doc("Get Stats")
    @facial_ns.expect(facial_stats)
    @facial_ns.response(200, "Logs fetched successfully.")
    @facial_ns.response(400, "Validation Error")
    def put(self):
        data = facial_ns.payload
        try:
            req_fields = ["client_id", "factory_id"]
            for field in req_fields:
                if field not in data or not data[field]:
                    return {"message": f"{field} is missing."}, 400
            
            stats = facial_func.get_stats(data)
            
            return {
                "message": "Facial recognition logs fetched successfully.",
                "success": True,
                "data": stats
            }, 200
            
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})
            
            

@facial_ns.route("/camera_status")
@facial_ns.doc("Get Camera Status")
class FacialGetLiveLogs(Resource):
    @facial_ns.doc("Get Camera Status")
    @facial_ns.expect(cameraStatus)
    @facial_ns.response(200, "Get Cameras successfully.")
    @facial_ns.response(400, "Validation Error")
    def put(self):
        data = facial_ns.payload
        try:
            req_fields = ["client_id", "factory_id"]
            for field in req_fields:
                if field not in data or not data[field]:
                    return {"message": f"{field} is missing."}, 400
            
            cameraStatus = facial_func.camera_status(data)
            
            return {
                "message": "Camera status fetched successfully.",
                "success": True,
                "data": cameraStatus
            }, 200
            
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})
                       
            
@facial_ns.route("/daily_movement_summary")
@facial_ns.doc("Daily Movement Summary")
class FacialGetLogs(Resource):
    @facial_ns.doc("Get Logs")
    @facial_ns.expect(daily_movement_summary_payload)
    @facial_ns.response(200, "Daily Movement Summary fetched successfully.")
    @facial_ns.response(400, "Validation Error")
    def put(self):
        data = facial_ns.payload
        try:
            req_fields = ["client_id", "factory_id"]
            for field in req_fields:
                if field not in data or not data[field]:
                    return {"message": f"{field} is missing."}, 400
            
            # Validate date formats
            patterns = {
                "week": r"\d{4}-W\d{2}",
                "month": r"\d{4}-\d{2}",
                "date": r"\d{4}-\d{2}-\d{2}",
                "starting": r"\d{4}-\d{2}-\d{2}",
                "ending": r"\d{4}-\d{2}-\d{2}"
            }

            f = data.get("filters", {})
            for field, pattern in patterns.items():
                if f.get(field) and not re.match(pattern, f[field]):
                    return {"message": f"Invalid {field} format"}, 400
            
            stats = facial_func.get_daily_movement_summary(data)
            
            return {
                "message": "Daily Movement Summary fetched successfully.",
                "success": True,
                "data": stats
            }, 200
            
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})
            
            
            
            
