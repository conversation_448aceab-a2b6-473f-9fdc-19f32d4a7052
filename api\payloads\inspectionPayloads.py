# from api import factory_dashboard_ns
from api import inspection_ns

from flask_restx import fields

filters_payload = inspection_ns.model(
    "Payload with all filters",
    {

        "filters": fields.Nested(inspection_ns.model('Inspection Filters', {
            "date": fields.String(default="2025-04-12", description="Date - format (YYYY-MM-DD)"),
            "week": fields.String(default="2025-W15", description="Week - format (YYYY-WWW)"),
            "month": fields.String(default="2025-04", description="Month - format (YYYY-MM)"),
            "starting": fields.String(default="", description="Starting - format (YYYY-MM-DD)"),
            "ending": fields.String(default="", description="Ending - format (YYYY-MM-DD)"),
            "shift": fields.String(default="", description="Shift"),
            # "alert_type": fields.String(default="entrance", description="Alert Type"),
        }), description="Payload with all filters"),

        "client_id": fields.Integer(
            default=1, required=True, description="The associated Client ID"
        ),
         "factory_id": fields.Integer(
            default=1, required=True, description="The associated Factory ID"
        ),
    },
)



# Define models for the nested data
inspection_alert_validation_model = inspection_ns.model(
    "Inspection Alert Validation Model",
    {
        "client_id": fields.Integer(
             default=1,required=True, description="Client ID"
        ),
        "factory_id": fields.Integer(
            default=1,required=True, description="Factory ID"
        ),
        "user_id": fields.Integer(
            default=1,required=True, description="User ID"
        ),
        "object_id": fields.Integer(
            default=1,required=True, description="Object ID"
        ),
        "validation_reason_id": fields.Integer(
            default=1,required=True, description="Validation reason ID"
        ),
        "model_id": fields.Integer(
            default=1,required=True, description="Model ID"
        ),
        "frame_id": fields.Integer(
            default=1,required=True, description="Alert frame ID"
        )
    },
)

    
# Define models for the nested data
inspection_operations_model = inspection_ns.model(
    "Inspection Operations Model",
    {
        
        "client_id": fields.Integer(
             default=1,required=True, description="Client ID"
        ),
        "factory_id": fields.Integer(
            default=1,required=True, description="Factory ID"
        ),
        "user_id": fields.Integer(
            default=1,required=True, description="User ID"
        ),
        "operation_type_id": fields.Integer(
            default=1,required=True, description="Operation Type ID"
        ),
    },
)

# Define models for the nested data
inspection_alert_count_model = inspection_ns.model(
    "Inspection Alert Count Model",
    {
        "client_id": fields.Integer(
             default=1,required=True, description="Client ID"
        ),
        "factory_id": fields.Integer(
            default=1,required=True, description="Factory ID"
        ),
        "object_id": fields.Integer(
            default=1,required=True, description="Object ID"
        ),
        "model_id": fields.Integer(
            default=1,required=True, description="Model ID"
        ),
        "count": fields.Integer(
            default=10,required=True, description="Alert count"
        ),
        "date": fields.Date(
            default="2025-04-22",required=True, description="Date"
        ),
    },
)

# Define the main payload model
inspection_sync_payload_model = inspection_ns.model(
    "Inspection Sync Payload",
    {
        "inspection_alert_validation": fields.List(
            fields.Nested(inspection_alert_validation_model),
            required=True,
            description="List of inspection alert validation records",
        ),
        "inspection_operations": fields.List(
            fields.Nested(inspection_operations_model),
            required=True,
            description="List of inspection operation records",
        ),
        "inspection_alert_counts": fields.List(
            fields.Nested(inspection_alert_count_model),
            required=True,
            description="List of inspection alert count records",
        ),
    },
)
